import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/service/database/database.dart';

/// 导出任务数据适配器
/// 用于在数据库实体和业务模型之间进行转换
class ExportTaskAdapter {
  /// 将数据库实体转换为业务模型
  static ExportRecord fromEntity(ExportTaskEntityData entity) {
    // 解析exportFileConfig JSON为ExportFile列表
    // List<ExportFile> exportFiles = [];

    return ExportRecord(
      guid: entity.guid,
      name: entity.name.isEmpty ? null : entity.name,
      showName: entity.showName.isEmpty ? null : entity.showName,
      exportPaths: entity.exportPaths.split(','),
      exportState: entity.exportState,
      createTime: entity.createTime,
      operateTime: entity.operateTime,
      successNum: entity.successNum,
      itemCount: entity.itemCount,
      errorNum: entity.errorNum,
      errorMessage: entity.errorMessage,
      isSample: false,
    );
  }

  /// 批量转换数据库实体列表为业务模型列表
  static List<ExportRecord> fromEntityList(
      List<ExportTaskEntityData> entities) {
    return entities.map((entity) => fromEntity(entity)).toList();
  }
}
