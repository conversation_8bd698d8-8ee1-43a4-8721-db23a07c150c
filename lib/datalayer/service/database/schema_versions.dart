import 'package:drift/internal/versioned_schema.dart' as i0;
import 'package:drift/drift.dart' as i1;
import 'package:drift/drift.dart'; // ignore_for_file: type=lint,unused_import

// GENERATED BY drift_dev, DO NOT MODIFY.
final class Schema2 extends i0.VersionedSchema {
  Schema2({required super.database}) : super(version: 2);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    projectEntity,
    userEntity,
    workspaceEntity,
    workspaceFileEntity,
    exportTokenEntity,
    creatorInfoEntity,
  ];
  late final Shape0 projectEntity = Shape0(
      source: i0.VersionedTable(
        entityName: 'project_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(project_id)',
        ],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_9,
          _column_10,
          _column_11,
          _column_12,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 userEntity = Shape1(
      source: i0.VersionedTable(
        entityName: 'user_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(id)',
        ],
        columns: [
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_17,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_25,
          _column_26,
          _column_27,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape2 workspaceEntity = Shape2(
      source: i0.VersionedTable(
        entityName: 'workspace_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(workspace_id)',
        ],
        columns: [
          _column_28,
          _column_29,
          _column_30,
          _column_31,
          _column_32,
          _column_33,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape3 workspaceFileEntity = Shape3(
      source: i0.VersionedTable(
        entityName: 'workspace_file_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(file_id)',
        ],
        columns: [
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_39,
          _column_40,
          _column_41,
          _column_32,
          _column_31,
          _column_42,
          _column_43,
          _column_44,
          _column_45,
          _column_46,
          _column_47,
          _column_48,
          _column_49,
          _column_50,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 exportTokenEntity = Shape4(
      source: i0.VersionedTable(
        entityName: 'export_token_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(user_id, image_p_hash)',
        ],
        columns: [
          _column_51,
          _column_52,
          _column_53,
          _column_54,
          _column_55,
          _column_56,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 creatorInfoEntity = Shape5(
      source: i0.VersionedTable(
        entityName: 'creator_info_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(uid)',
        ],
        columns: [
          _column_15,
          _column_57,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape0 extends i0.VersionedTable {
  Shape0({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get projectId =>
      columnsByName['project_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get version =>
      columnsByName['version']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get author =>
      columnsByName['author']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get coverImages =>
      columnsByName['cover_images']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get description =>
      columnsByName['description']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get createdDate =>
      columnsByName['created_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get updateDate =>
      columnsByName['update_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get outputFolder =>
      columnsByName['output_folder']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportFileType =>
      columnsByName['export_file_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get quality =>
      columnsByName['quality']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isReplace =>
      columnsByName['is_replace']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get transferSRGB =>
      columnsByName['transfer_s_r_g_b']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_0(String aliasedName) =>
    i1.GeneratedColumn<String>('name', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_1(String aliasedName) =>
    i1.GeneratedColumn<String>('project_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_2(String aliasedName) =>
    i1.GeneratedColumn<String>('version', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_3(String aliasedName) =>
    i1.GeneratedColumn<String>('author', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_4(String aliasedName) =>
    i1.GeneratedColumn<String>('cover_images', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_5(String aliasedName) =>
    i1.GeneratedColumn<String>('description', aliasedName, true,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<DateTime> _column_6(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('created_date', aliasedName, true,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<DateTime> _column_7(String aliasedName) =>
    i1.GeneratedColumn<DateTime>('update_date', aliasedName, true,
        type: i1.DriftSqlType.dateTime);
i1.GeneratedColumn<String> _column_8(String aliasedName) =>
    i1.GeneratedColumn<String>('output_folder', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<int> _column_9(String aliasedName) =>
    i1.GeneratedColumn<int>('export_file_type', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<int> _column_10(String aliasedName) =>
    i1.GeneratedColumn<int>('quality', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(100));
i1.GeneratedColumn<bool> _column_11(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_replace', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_replace" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<bool> _column_12(String aliasedName) =>
    i1.GeneratedColumn<bool>('transfer_s_r_g_b', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("transfer_s_r_g_b" IN (0, 1))'),
        defaultValue: const Constant(false));

class Shape1 extends i0.VersionedTable {
  Shape1({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get id =>
      columnsByName['id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get username =>
      columnsByName['username']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get uid =>
      columnsByName['uid']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get phoneNumber =>
      columnsByName['phone_number']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get token =>
      columnsByName['token']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get tokenExpiredDate =>
      columnsByName['token_expired_date']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get tokenEnd =>
      columnsByName['token_end']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get firstLogin =>
      columnsByName['first_login']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get lastLoginTime =>
      columnsByName['last_login_time']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get regDateTime =>
      columnsByName['reg_date_time']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get cc =>
      columnsByName['cc']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get role =>
      columnsByName['role']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get used =>
      columnsByName['used']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get enable =>
      columnsByName['enable']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get lastLoginStoreId =>
      columnsByName['last_login_store_id']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_13(String aliasedName) =>
    i1.GeneratedColumn<String>('id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_14(String aliasedName) =>
    i1.GeneratedColumn<String>('username', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_15(String aliasedName) =>
    i1.GeneratedColumn<String>('uid', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_16(String aliasedName) =>
    i1.GeneratedColumn<String>('phone_number', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_17(String aliasedName) =>
    i1.GeneratedColumn<String>('token', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_18(String aliasedName) =>
    i1.GeneratedColumn<int>('token_expired_date', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_19(String aliasedName) =>
    i1.GeneratedColumn<String>('token_end', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_20(String aliasedName) =>
    i1.GeneratedColumn<int>('first_login', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<String> _column_21(String aliasedName) =>
    i1.GeneratedColumn<String>('last_login_time', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<String> _column_22(String aliasedName) =>
    i1.GeneratedColumn<String>('reg_date_time', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<int> _column_23(String aliasedName) =>
    i1.GeneratedColumn<int>('cc', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(86));
i1.GeneratedColumn<String> _column_24(String aliasedName) =>
    i1.GeneratedColumn<String>('role', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant('creator'));
i1.GeneratedColumn<String> _column_25(String aliasedName) =>
    i1.GeneratedColumn<String>('used', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant('0'));
i1.GeneratedColumn<int> _column_26(String aliasedName) =>
    i1.GeneratedColumn<int>('enable', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(1));
i1.GeneratedColumn<String> _column_27(String aliasedName) =>
    i1.GeneratedColumn<String>('last_login_store_id', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));

class Shape2 extends i0.VersionedTable {
  Shape2({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get workspaceId =>
      columnsByName['workspace_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get workspaceName =>
      columnsByName['workspace_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get currentFileId =>
      columnsByName['current_file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get lastEditTime =>
      columnsByName['last_edit_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get projectId =>
      columnsByName['project_id']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_28(String aliasedName) =>
    i1.GeneratedColumn<String>('workspace_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_29(String aliasedName) =>
    i1.GeneratedColumn<String>('workspace_name', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_30(String aliasedName) =>
    i1.GeneratedColumn<String>('current_file_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_31(String aliasedName) =>
    i1.GeneratedColumn<int>('create_time', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_32(String aliasedName) =>
    i1.GeneratedColumn<int>('last_edit_time', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_33(String aliasedName) =>
    i1.GeneratedColumn<String>('project_id', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES project_entity (project_id)'));

class Shape3 extends i0.VersionedTable {
  Shape3({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get fileId =>
      columnsByName['file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get edited =>
      columnsByName['edited']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get stars =>
      columnsByName['stars']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get exported =>
      columnsByName['exported']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get orgPath =>
      columnsByName['org_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportTime =>
      columnsByName['export_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get format =>
      columnsByName['format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get broken =>
      columnsByName['broken']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get lastEditTime =>
      columnsByName['last_edit_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get size =>
      columnsByName['size']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get width =>
      columnsByName['width']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get height =>
      columnsByName['height']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get orientation =>
      columnsByName['orientation']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get iccType =>
      columnsByName['icc_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isRaw =>
      columnsByName['is_raw']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get rawPath =>
      columnsByName['raw_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get converted =>
      columnsByName['converted']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get workspaceId =>
      columnsByName['workspace_id']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_34(String aliasedName) =>
    i1.GeneratedColumn<String>('file_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_35(String aliasedName) =>
    i1.GeneratedColumn<bool>('edited', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("edited" IN (0, 1))'));
i1.GeneratedColumn<int> _column_36(String aliasedName) =>
    i1.GeneratedColumn<int>('stars', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<bool> _column_37(String aliasedName) =>
    i1.GeneratedColumn<bool>('exported', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("exported" IN (0, 1))'));
i1.GeneratedColumn<String> _column_38(String aliasedName) =>
    i1.GeneratedColumn<String>('org_path', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_39(String aliasedName) =>
    i1.GeneratedColumn<int>('export_time', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_40(String aliasedName) =>
    i1.GeneratedColumn<String>('format', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_41(String aliasedName) =>
    i1.GeneratedColumn<bool>('broken', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("broken" IN (0, 1))'));
i1.GeneratedColumn<int> _column_42(String aliasedName) =>
    i1.GeneratedColumn<int>('size', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_43(String aliasedName) =>
    i1.GeneratedColumn<int>('width', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_44(String aliasedName) =>
    i1.GeneratedColumn<int>('height', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_45(String aliasedName) =>
    i1.GeneratedColumn<int>('orientation', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<int> _column_46(String aliasedName) =>
    i1.GeneratedColumn<int>('icc_type', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<bool> _column_47(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_raw', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_raw" IN (0, 1))'));
i1.GeneratedColumn<String> _column_48(String aliasedName) =>
    i1.GeneratedColumn<String>('raw_path', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<bool> _column_49(String aliasedName) =>
    i1.GeneratedColumn<bool>('converted', aliasedName, false,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("converted" IN (0, 1))'));
i1.GeneratedColumn<String> _column_50(String aliasedName) =>
    i1.GeneratedColumn<String>('workspace_id', aliasedName, false,
        type: i1.DriftSqlType.string,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'REFERENCES workspace_entity (workspace_id)'));

class Shape4 extends i0.VersionedTable {
  Shape4({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get imagePHash =>
      columnsByName['image_p_hash']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get userId =>
      columnsByName['user_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get tokenId =>
      columnsByName['token_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get key =>
      columnsByName['key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get expireAt =>
      columnsByName['expire_at']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get imageName =>
      columnsByName['image_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<String> _column_51(String aliasedName) =>
    i1.GeneratedColumn<String>('image_p_hash', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_52(String aliasedName) =>
    i1.GeneratedColumn<String>('user_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_53(String aliasedName) =>
    i1.GeneratedColumn<String>('token_id', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_54(String aliasedName) =>
    i1.GeneratedColumn<String>('key', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_55(String aliasedName) =>
    i1.GeneratedColumn<int>('expire_at', aliasedName, false,
        type: i1.DriftSqlType.int);
i1.GeneratedColumn<String> _column_56(String aliasedName) =>
    i1.GeneratedColumn<String>('image_name', aliasedName, true,
        type: i1.DriftSqlType.string);

class Shape5 extends i0.VersionedTable {
  Shape5({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get uid =>
      columnsByName['uid']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get creatorMobile =>
      columnsByName['creator_mobile']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_57(String aliasedName) =>
    i1.GeneratedColumn<String>('creator_mobile', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));

final class Schema3 extends i0.VersionedSchema {
  Schema3({required super.database}) : super(version: 3);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    projectEntity,
    userEntity,
    workspaceEntity,
    workspaceFileEntity,
    exportTokenEntity,
    creatorInfoEntity,
  ];
  late final Shape6 projectEntity = Shape6(
      source: i0.VersionedTable(
        entityName: 'project_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(project_id)',
        ],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_8,
          _column_9,
          _column_10,
          _column_11,
          _column_12,
          _column_58,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 userEntity = Shape1(
      source: i0.VersionedTable(
        entityName: 'user_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(id)',
        ],
        columns: [
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_17,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_25,
          _column_26,
          _column_27,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 workspaceEntity = Shape7(
      source: i0.VersionedTable(
        entityName: 'workspace_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(workspace_id)',
        ],
        columns: [
          _column_28,
          _column_29,
          _column_30,
          _column_31,
          _column_32,
          _column_33,
          _column_59,
          _column_60,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape8 workspaceFileEntity = Shape8(
      source: i0.VersionedTable(
        entityName: 'workspace_file_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(file_id)',
        ],
        columns: [
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_39,
          _column_40,
          _column_41,
          _column_32,
          _column_31,
          _column_42,
          _column_43,
          _column_44,
          _column_45,
          _column_46,
          _column_47,
          _column_48,
          _column_49,
          _column_50,
          _column_61,
          _column_62,
          _column_63,
          _column_64,
          _column_65,
          _column_66,
          _column_67,
          _column_68,
          _column_69,
          _column_70,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 exportTokenEntity = Shape4(
      source: i0.VersionedTable(
        entityName: 'export_token_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(user_id, image_p_hash)',
        ],
        columns: [
          _column_51,
          _column_52,
          _column_53,
          _column_54,
          _column_55,
          _column_56,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 creatorInfoEntity = Shape5(
      source: i0.VersionedTable(
        entityName: 'creator_info_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(uid)',
        ],
        columns: [
          _column_15,
          _column_57,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape6 extends i0.VersionedTable {
  Shape6({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get projectId =>
      columnsByName['project_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get version =>
      columnsByName['version']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get author =>
      columnsByName['author']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get coverImages =>
      columnsByName['cover_images']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get description =>
      columnsByName['description']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get createdDate =>
      columnsByName['created_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get updateDate =>
      columnsByName['update_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<String> get outputFolder =>
      columnsByName['output_folder']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportFileType =>
      columnsByName['export_file_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get quality =>
      columnsByName['quality']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isReplace =>
      columnsByName['is_replace']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get transferSRGB =>
      columnsByName['transfer_s_r_g_b']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get workspaceVersion =>
      columnsByName['workspace_version']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_58(String aliasedName) =>
    i1.GeneratedColumn<int>('workspace_version', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(1));

class Shape7 extends i0.VersionedTable {
  Shape7({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get workspaceId =>
      columnsByName['workspace_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get workspaceName =>
      columnsByName['workspace_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get currentFileId =>
      columnsByName['current_file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get lastEditTime =>
      columnsByName['last_edit_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get projectId =>
      columnsByName['project_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get filterValue =>
      columnsByName['filter_value']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get sortValue =>
      columnsByName['sort_value']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_59(String aliasedName) =>
    i1.GeneratedColumn<String>('filter_value', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant('{}'));
i1.GeneratedColumn<String> _column_60(String aliasedName) =>
    i1.GeneratedColumn<String>('sort_value', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant('{}'));

class Shape8 extends i0.VersionedTable {
  Shape8({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get fileId =>
      columnsByName['file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get edited =>
      columnsByName['edited']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get stars =>
      columnsByName['stars']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get exported =>
      columnsByName['exported']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get orgPath =>
      columnsByName['org_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportTime =>
      columnsByName['export_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get format =>
      columnsByName['format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get broken =>
      columnsByName['broken']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get lastEditTime =>
      columnsByName['last_edit_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get size =>
      columnsByName['size']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get width =>
      columnsByName['width']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get height =>
      columnsByName['height']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get orientation =>
      columnsByName['orientation']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get iccType =>
      columnsByName['icc_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isRaw =>
      columnsByName['is_raw']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get rawPath =>
      columnsByName['raw_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get converted =>
      columnsByName['converted']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get workspaceId =>
      columnsByName['workspace_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get fileName =>
      columnsByName['file_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get iconized =>
      columnsByName['iconized']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get midIconized =>
      columnsByName['mid_iconized']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get captureTime =>
      columnsByName['capture_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isOverSize =>
      columnsByName['is_over_size']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get faceCount =>
      columnsByName['face_count']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get binFormat =>
      columnsByName['bin_format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get rawAutoExpose =>
      columnsByName['raw_auto_expose']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get rawAutoAdjustType =>
      columnsByName['raw_auto_adjust_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isDeleted =>
      columnsByName['is_deleted']! as i1.GeneratedColumn<bool>;
}

i1.GeneratedColumn<String> _column_61(String aliasedName) =>
    i1.GeneratedColumn<String>('file_name', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<bool> _column_62(String aliasedName) =>
    i1.GeneratedColumn<bool>('iconized', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("iconized" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<bool> _column_63(String aliasedName) =>
    i1.GeneratedColumn<bool>('mid_iconized', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("mid_iconized" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<int> _column_64(String aliasedName) =>
    i1.GeneratedColumn<int>('capture_time', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<bool> _column_65(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_over_size', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_over_size" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<int> _column_66(String aliasedName) =>
    i1.GeneratedColumn<int>('face_count', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<String> _column_67(String aliasedName) =>
    i1.GeneratedColumn<String>('bin_format', aliasedName, true,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<bool> _column_68(String aliasedName) =>
    i1.GeneratedColumn<bool>('raw_auto_expose', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("raw_auto_expose" IN (0, 1))'),
        defaultValue: const Constant(false));
i1.GeneratedColumn<int> _column_69(String aliasedName) =>
    i1.GeneratedColumn<int>('raw_auto_adjust_type', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<bool> _column_70(String aliasedName) =>
    i1.GeneratedColumn<bool>('is_deleted', aliasedName, true,
        type: i1.DriftSqlType.bool,
        defaultConstraints: i1.GeneratedColumn.constraintIsAlways(
            'CHECK ("is_deleted" IN (0, 1))'),
        defaultValue: const Constant(false));

final class Schema4 extends i0.VersionedSchema {
  Schema4({required super.database}) : super(version: 4);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    projectEntity,
    userEntity,
    workspaceEntity,
    workspaceFileEntity,
    exportTokenEntity,
    creatorInfoEntity,
  ];
  late final Shape9 projectEntity = Shape9(
      source: i0.VersionedTable(
        entityName: 'project_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(project_id)',
        ],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_71,
          _column_8,
          _column_9,
          _column_10,
          _column_11,
          _column_12,
          _column_58,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 userEntity = Shape1(
      source: i0.VersionedTable(
        entityName: 'user_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(id)',
        ],
        columns: [
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_17,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_25,
          _column_26,
          _column_27,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 workspaceEntity = Shape7(
      source: i0.VersionedTable(
        entityName: 'workspace_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(workspace_id)',
        ],
        columns: [
          _column_28,
          _column_29,
          _column_30,
          _column_31,
          _column_32,
          _column_33,
          _column_59,
          _column_60,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape8 workspaceFileEntity = Shape8(
      source: i0.VersionedTable(
        entityName: 'workspace_file_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(file_id)',
        ],
        columns: [
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_39,
          _column_40,
          _column_41,
          _column_32,
          _column_31,
          _column_42,
          _column_43,
          _column_44,
          _column_45,
          _column_46,
          _column_47,
          _column_48,
          _column_49,
          _column_50,
          _column_61,
          _column_62,
          _column_63,
          _column_64,
          _column_65,
          _column_66,
          _column_67,
          _column_68,
          _column_69,
          _column_70,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape4 exportTokenEntity = Shape4(
      source: i0.VersionedTable(
        entityName: 'export_token_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(user_id, image_p_hash)',
        ],
        columns: [
          _column_51,
          _column_52,
          _column_53,
          _column_54,
          _column_55,
          _column_56,
          _column_31,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 creatorInfoEntity = Shape5(
      source: i0.VersionedTable(
        entityName: 'creator_info_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(uid)',
        ],
        columns: [
          _column_15,
          _column_57,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape9 extends i0.VersionedTable {
  Shape9({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get projectId =>
      columnsByName['project_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get version =>
      columnsByName['version']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get author =>
      columnsByName['author']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get coverImages =>
      columnsByName['cover_images']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get description =>
      columnsByName['description']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<DateTime> get createdDate =>
      columnsByName['created_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<DateTime> get updateDate =>
      columnsByName['update_date']! as i1.GeneratedColumn<DateTime>;
  i1.GeneratedColumn<int> get projectType =>
      columnsByName['project_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get outputFolder =>
      columnsByName['output_folder']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportFileType =>
      columnsByName['export_file_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get quality =>
      columnsByName['quality']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isReplace =>
      columnsByName['is_replace']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get transferSRGB =>
      columnsByName['transfer_s_r_g_b']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get workspaceVersion =>
      columnsByName['workspace_version']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_71(String aliasedName) =>
    i1.GeneratedColumn<int>('project_type', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));

final class Schema5 extends i0.VersionedSchema {
  Schema5({required super.database}) : super(version: 5);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    projectEntity,
    userEntity,
    workspaceEntity,
    workspaceFileEntity,
    exportTokenEntity,
    creatorInfoEntity,
    fileOperationHistoryEntity,
  ];
  late final Shape9 projectEntity = Shape9(
      source: i0.VersionedTable(
        entityName: 'project_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(project_id)',
        ],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_71,
          _column_8,
          _column_9,
          _column_10,
          _column_11,
          _column_12,
          _column_58,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 userEntity = Shape1(
      source: i0.VersionedTable(
        entityName: 'user_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(id)',
        ],
        columns: [
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_17,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_25,
          _column_26,
          _column_27,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 workspaceEntity = Shape7(
      source: i0.VersionedTable(
        entityName: 'workspace_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(workspace_id)',
        ],
        columns: [
          _column_28,
          _column_29,
          _column_30,
          _column_31,
          _column_32,
          _column_33,
          _column_59,
          _column_60,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape10 workspaceFileEntity = Shape10(
      source: i0.VersionedTable(
        entityName: 'workspace_file_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(file_id)',
        ],
        columns: [
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_39,
          _column_40,
          _column_41,
          _column_32,
          _column_31,
          _column_42,
          _column_43,
          _column_44,
          _column_45,
          _column_46,
          _column_47,
          _column_48,
          _column_49,
          _column_50,
          _column_61,
          _column_62,
          _column_63,
          _column_64,
          _column_65,
          _column_66,
          _column_67,
          _column_68,
          _column_69,
          _column_70,
          _column_72,
          _column_73,
          _column_74,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape11 exportTokenEntity = Shape11(
      source: i0.VersionedTable(
        entityName: 'export_token_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(user_id, image_p_hash, export_type)',
        ],
        columns: [
          _column_51,
          _column_52,
          _column_53,
          _column_54,
          _column_55,
          _column_56,
          _column_31,
          _column_75,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 creatorInfoEntity = Shape5(
      source: i0.VersionedTable(
        entityName: 'creator_info_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(uid)',
        ],
        columns: [
          _column_15,
          _column_57,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape12 fileOperationHistoryEntity = Shape12(
      source: i0.VersionedTable(
        entityName: 'file_operation_history_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_76,
          _column_34,
          _column_31,
          _column_77,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape10 extends i0.VersionedTable {
  Shape10({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get fileId =>
      columnsByName['file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get edited =>
      columnsByName['edited']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get stars =>
      columnsByName['stars']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get exported =>
      columnsByName['exported']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get orgPath =>
      columnsByName['org_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportTime =>
      columnsByName['export_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get format =>
      columnsByName['format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get broken =>
      columnsByName['broken']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get lastEditTime =>
      columnsByName['last_edit_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get size =>
      columnsByName['size']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get width =>
      columnsByName['width']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get height =>
      columnsByName['height']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get orientation =>
      columnsByName['orientation']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get iccType =>
      columnsByName['icc_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isRaw =>
      columnsByName['is_raw']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get rawPath =>
      columnsByName['raw_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get converted =>
      columnsByName['converted']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<String> get workspaceId =>
      columnsByName['workspace_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get fileName =>
      columnsByName['file_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get iconized =>
      columnsByName['iconized']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<bool> get midIconized =>
      columnsByName['mid_iconized']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get captureTime =>
      columnsByName['capture_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isOverSize =>
      columnsByName['is_over_size']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get faceCount =>
      columnsByName['face_count']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get binFormat =>
      columnsByName['bin_format']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<bool> get rawAutoExpose =>
      columnsByName['raw_auto_expose']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get rawAutoAdjustType =>
      columnsByName['raw_auto_adjust_type']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<bool> get isDeleted =>
      columnsByName['is_deleted']! as i1.GeneratedColumn<bool>;
  i1.GeneratedColumn<int> get n8Exported =>
      columnsByName['n8_exported']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<BigInt> get n8ExportTime =>
      columnsByName['n8_export_time']! as i1.GeneratedColumn<BigInt>;
  i1.GeneratedColumn<int> get fileExportedState =>
      columnsByName['file_exported_state']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<int> _column_72(String aliasedName) =>
    i1.GeneratedColumn<int>('n8_exported', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<BigInt> _column_73(String aliasedName) =>
    i1.GeneratedColumn<BigInt>('n8_export_time', aliasedName, true,
        type: i1.DriftSqlType.bigInt, defaultValue: Constant(BigInt.from(0)));
i1.GeneratedColumn<int> _column_74(String aliasedName) =>
    i1.GeneratedColumn<int>('file_exported_state', aliasedName, true,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));

class Shape11 extends i0.VersionedTable {
  Shape11({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get imagePHash =>
      columnsByName['image_p_hash']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get userId =>
      columnsByName['user_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get tokenId =>
      columnsByName['token_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get key =>
      columnsByName['key']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get expireAt =>
      columnsByName['expire_at']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get imageName =>
      columnsByName['image_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get exportType =>
      columnsByName['export_type']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<String> _column_75(String aliasedName) =>
    i1.GeneratedColumn<String>('export_type', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant('retouch'));

class Shape12 extends i0.VersionedTable {
  Shape12({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<int> get id =>
      columnsByName['id']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get fileId =>
      columnsByName['file_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get extraData =>
      columnsByName['extra_data']! as i1.GeneratedColumn<String>;
}

i1.GeneratedColumn<int> _column_76(String aliasedName) =>
    i1.GeneratedColumn<int>('id', aliasedName, false,
        hasAutoIncrement: true,
        type: i1.DriftSqlType.int,
        defaultConstraints:
            i1.GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
i1.GeneratedColumn<String> _column_77(String aliasedName) =>
    i1.GeneratedColumn<String>('extra_data', aliasedName, true,
        type: i1.DriftSqlType.string);

final class Schema6 extends i0.VersionedSchema {
  Schema6({required super.database}) : super(version: 6);
  @override
  late final List<i1.DatabaseSchemaEntity> entities = [
    projectEntity,
    userEntity,
    workspaceEntity,
    workspaceFileEntity,
    exportTokenEntity,
    creatorInfoEntity,
    fileOperationHistoryEntity,
    exportTaskEntity,
  ];
  late final Shape9 projectEntity = Shape9(
      source: i0.VersionedTable(
        entityName: 'project_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(project_id)',
        ],
        columns: [
          _column_0,
          _column_1,
          _column_2,
          _column_3,
          _column_4,
          _column_5,
          _column_6,
          _column_7,
          _column_71,
          _column_8,
          _column_9,
          _column_10,
          _column_11,
          _column_12,
          _column_58,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape1 userEntity = Shape1(
      source: i0.VersionedTable(
        entityName: 'user_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(id)',
        ],
        columns: [
          _column_13,
          _column_14,
          _column_15,
          _column_16,
          _column_17,
          _column_18,
          _column_19,
          _column_20,
          _column_21,
          _column_22,
          _column_23,
          _column_24,
          _column_25,
          _column_26,
          _column_27,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape7 workspaceEntity = Shape7(
      source: i0.VersionedTable(
        entityName: 'workspace_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(workspace_id)',
        ],
        columns: [
          _column_28,
          _column_29,
          _column_30,
          _column_31,
          _column_32,
          _column_33,
          _column_59,
          _column_60,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape10 workspaceFileEntity = Shape10(
      source: i0.VersionedTable(
        entityName: 'workspace_file_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(file_id)',
        ],
        columns: [
          _column_34,
          _column_35,
          _column_36,
          _column_37,
          _column_38,
          _column_39,
          _column_40,
          _column_41,
          _column_32,
          _column_31,
          _column_42,
          _column_43,
          _column_44,
          _column_45,
          _column_46,
          _column_47,
          _column_48,
          _column_49,
          _column_50,
          _column_61,
          _column_62,
          _column_63,
          _column_64,
          _column_65,
          _column_66,
          _column_67,
          _column_68,
          _column_69,
          _column_70,
          _column_72,
          _column_73,
          _column_74,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape11 exportTokenEntity = Shape11(
      source: i0.VersionedTable(
        entityName: 'export_token_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(user_id, image_p_hash, export_type)',
        ],
        columns: [
          _column_51,
          _column_52,
          _column_53,
          _column_54,
          _column_55,
          _column_56,
          _column_31,
          _column_75,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape5 creatorInfoEntity = Shape5(
      source: i0.VersionedTable(
        entityName: 'creator_info_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(uid)',
        ],
        columns: [
          _column_15,
          _column_57,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape12 fileOperationHistoryEntity = Shape12(
      source: i0.VersionedTable(
        entityName: 'file_operation_history_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [],
        columns: [
          _column_76,
          _column_34,
          _column_31,
          _column_77,
        ],
        attachedDatabase: database,
      ),
      alias: null);
  late final Shape13 exportTaskEntity = Shape13(
      source: i0.VersionedTable(
        entityName: 'export_task_entity',
        withoutRowId: false,
        isStrict: false,
        tableConstraints: [
          'PRIMARY KEY(guid)',
        ],
        columns: [
          _column_78,
          _column_52,
          _column_79,
          _column_80,
          _column_0,
          _column_81,
          _column_82,
          _column_83,
          _column_84,
          _column_31,
          _column_85,
          _column_86,
          _column_87,
          _column_88,
        ],
        attachedDatabase: database,
      ),
      alias: null);
}

class Shape13 extends i0.VersionedTable {
  Shape13({required super.source, required super.alias}) : super.aliased();
  i1.GeneratedColumn<String> get guid =>
      columnsByName['guid']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get userId =>
      columnsByName['user_id']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get cachePath =>
      columnsByName['cache_path']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get exportFileConfig =>
      columnsByName['export_file_config']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get name =>
      columnsByName['name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get showName =>
      columnsByName['show_name']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<String> get exportPaths =>
      columnsByName['export_paths']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get exportState =>
      columnsByName['export_state']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get operateTime =>
      columnsByName['operate_time']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get createTime =>
      columnsByName['create_time']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get itemCount =>
      columnsByName['item_count']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<int> get successNum =>
      columnsByName['success_num']! as i1.GeneratedColumn<int>;
  i1.GeneratedColumn<String> get errorMessage =>
      columnsByName['error_message']! as i1.GeneratedColumn<String>;
  i1.GeneratedColumn<int> get errorNum =>
      columnsByName['error_num']! as i1.GeneratedColumn<int>;
}

i1.GeneratedColumn<String> _column_78(String aliasedName) =>
    i1.GeneratedColumn<String>('guid', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_79(String aliasedName) =>
    i1.GeneratedColumn<String>('cache_path', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_80(String aliasedName) =>
    i1.GeneratedColumn<String>('export_file_config', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_81(String aliasedName) =>
    i1.GeneratedColumn<String>('show_name', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<String> _column_82(String aliasedName) =>
    i1.GeneratedColumn<String>('export_paths', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_83(String aliasedName) =>
    i1.GeneratedColumn<int>('export_state', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<String> _column_84(String aliasedName) =>
    i1.GeneratedColumn<String>('operate_time', aliasedName, false,
        type: i1.DriftSqlType.string);
i1.GeneratedColumn<int> _column_85(String aliasedName) =>
    i1.GeneratedColumn<int>('item_count', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<int> _column_86(String aliasedName) =>
    i1.GeneratedColumn<int>('success_num', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i1.GeneratedColumn<String> _column_87(String aliasedName) =>
    i1.GeneratedColumn<String>('error_message', aliasedName, false,
        type: i1.DriftSqlType.string, defaultValue: const Constant(''));
i1.GeneratedColumn<int> _column_88(String aliasedName) =>
    i1.GeneratedColumn<int>('error_num', aliasedName, false,
        type: i1.DriftSqlType.int, defaultValue: const Constant(0));
i0.MigrationStepWithVersion migrationSteps({
  required Future<void> Function(i1.Migrator m, Schema2 schema) from1To2,
  required Future<void> Function(i1.Migrator m, Schema3 schema) from2To3,
  required Future<void> Function(i1.Migrator m, Schema4 schema) from3To4,
  required Future<void> Function(i1.Migrator m, Schema5 schema) from4To5,
  required Future<void> Function(i1.Migrator m, Schema6 schema) from5To6,
}) {
  return (currentVersion, database) async {
    switch (currentVersion) {
      case 1:
        final schema = Schema2(database: database);
        final migrator = i1.Migrator(database, schema);
        await from1To2(migrator, schema);
        return 2;
      case 2:
        final schema = Schema3(database: database);
        final migrator = i1.Migrator(database, schema);
        await from2To3(migrator, schema);
        return 3;
      case 3:
        final schema = Schema4(database: database);
        final migrator = i1.Migrator(database, schema);
        await from3To4(migrator, schema);
        return 4;
      case 4:
        final schema = Schema5(database: database);
        final migrator = i1.Migrator(database, schema);
        await from4To5(migrator, schema);
        return 5;
      case 5:
        final schema = Schema6(database: database);
        final migrator = i1.Migrator(database, schema);
        await from5To6(migrator, schema);
        return 6;
      default:
        throw ArgumentError.value('Unknown migration from $currentVersion');
    }
  };
}

i1.OnUpgrade stepByStep({
  required Future<void> Function(i1.Migrator m, Schema2 schema) from1To2,
  required Future<void> Function(i1.Migrator m, Schema3 schema) from2To3,
  required Future<void> Function(i1.Migrator m, Schema4 schema) from3To4,
  required Future<void> Function(i1.Migrator m, Schema5 schema) from4To5,
  required Future<void> Function(i1.Migrator m, Schema6 schema) from5To6,
}) =>
    i0.VersionedSchema.stepByStepHelper(
        step: migrationSteps(
      from1To2: from1To2,
      from2To3: from2To3,
      from3To4: from3To4,
      from4To5: from4To5,
      from5To6: from5To6,
    ));
