import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/database/adapters/export_task_adapter.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/datalayer/service/database/operater/export_task_db_operater.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'export_records_repository.dart';

class ExportRecordsRepositoryImpl3 implements ExportRecordsRepository {
  final DbOperater _dbOperater;
  final CurrentUserRepository _currentUserRepository;

  ExportRecordsRepositoryImpl3(this._dbOperater, this._currentUserRepository);

  @override
  Future<List<ExportRecord>> fetchExportRecord() async {
    try {
      final userId = await _currentUserRepository.getUserId();
      if (userId == null) {
        return [];
      }

      final exportTasks = await _dbOperater.getUserAllExportTasks(userId);
      // 使用适配器进行数据转换
      return ExportTaskAdapter.fromEntityList(exportTasks);
    } catch (e) {
      PGLog.e("加载导出记录失败: $e");
      return [];
    }
  }

  @override
  Future<List<ExportRecord>> fetchExportRecordsByGuids(
    List<String> guids,
  ) async {
    final exportTasks = await _dbOperater.fetchExportTasks(guids);
    return ExportTaskAdapter.fromEntityList(exportTasks);
  }

  @override
  Future<void> deleteExportRecord(String guid) async {
    try {
      await _dbOperater.deleteExportTask(guid);
    } catch (e) {
      PGLog.e("删除导出记录失败: $e");
    }
  }

  @override
  Future<void> updateExportRecord(ExportRecord record) async {
    // TODO: 更新导出记录，暂未实现
  }
}
