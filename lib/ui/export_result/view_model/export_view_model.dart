import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_models.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/ui/aigc_sample/services/file_path_service.dart';
import 'package:turing_art/ui/export_result/handler/export_path_viewer_handler.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/services/n8_layout_service.dart';
import 'package:turing_art/ui/export_result/use_case/export_use_case_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

@Deprecated("该类已废弃，请使用ExportViewModel2实现相关功能")
class ExportViewModel extends ChangeNotifier {
  final ExportUseCaseProvider _exportUseCaseProvider;
  final AigcMySampleExportManager _aigcMySampleExportManager;
  final CurrentUserRepository _currentUserRepository;
  final FilePathService _filePathService = FilePathService.forPlatform();
  late final ExportPathViewerHandler _exportPathViewerHandler;
  List<ExportUiStatus> _exportUiStatus = [];
  List<ExportUiStatus> get exportUiStatus => _exportUiStatus;
  // 用于AIGC导出数据
  List<ExportUiStatus> _aigcItems = [];
  List<ExportUiStatus> get aigcItems => _aigcItems;

  List<ExportRecord> _latestRecords = [];
  final Set<String> _deletedGuids = {};

  bool _isPolling = false;
  bool _isDisposed = false;

  bool _isFirstLoaded = false;
  bool get isFirstLoaded => _isFirstLoaded;

  // 导出类型选择相关状态
  ExportType _selectedExportType = ExportType.retouching; // 当前选择的导出类型
  ExportType get selectedExportType => _selectedExportType;

  ExportViewModel(
    this._exportUseCaseProvider,
    this._aigcMySampleExportManager,
    this._currentUserRepository, {
    ExportType? defaultExportType,
  }) {
    // 初始化handler
    _exportPathViewerHandler = ExportPathViewerHandler(
      _aigcMySampleExportManager,
      _filePathService,
    );

    // 如果指定了默认导出类型，则使用它
    if (defaultExportType != null) {
      _selectedExportType = defaultExportType;
    }

    _aigcMySampleExportManager.addListener(() {
      _updateAigcData();
    });
    _startPolling();
    _updateAigcData();
  }

  /// 获取是否显示N8按钮的权限
  bool _canShowN8Button() {
    return _currentUserRepository.store?.couldExportSample() ?? false;
  }

  /// 更新AIGC导出数据
  void _updateAigcData() {
    PGLog.d('开始更新AIGC导出数据');

    // 从AigcMySampleExportManager获取真实数据并转换
    final uiModels = _aigcMySampleExportManager.uiModelList;
    _aigcItems = _convertAigcUIModelsToExportUiStatus(uiModels);

    // 按创建时间排序
    _aigcItems.sort((a, b) => b.createTime.compareTo(a.createTime));

    PGLog.d('AIGC导出数据更新完成，共${_aigcItems.length}个项目');

    // 通知UI更新
    if (!_isDisposed) {
      notifyListeners();
    }
  }

  /// 将AigcMySampleExportUIModel转换为ExportUiStatus
  List<ExportUiStatus> _convertAigcUIModelsToExportUiStatus(
      List<AigcMySampleExportUIModel> uiModels) {
    final List<ExportUiStatus> exportItems = [];

    for (final uiModel in uiModels) {
      // 解析导出数量字符串，例如 "3/5"
      final exportNumParts = uiModel.exportNum.split('/');
      final successCount = int.tryParse(exportNumParts[0]) ?? 0;
      final totalCount = int.tryParse(exportNumParts[1]) ?? 0;

      // 根据进度状态转换为ExportState
      ExportState state;
      switch (uiModel.progressStatus) {
        case AigcMySampleExportProgressStatus.processing:
          state = ExportState.processing;
          break;
        case AigcMySampleExportProgressStatus.completed:
          state = ExportState.finish;
          break;
        case AigcMySampleExportProgressStatus.failed:
          state = ExportState.error;
          break;
      }

      final createTime = uiModel.updateTime.millisecondsSinceEpoch;

      // 成功时间（如果已完成）
      final successTime = createTime;

      exportItems.add(
        ExportUiStatus.createAigc(
          guid: uiModel.id, // 使用打样项目ID作为guid
          projectName: uiModel.name,
          state: state,
          totalCount: totalCount,
          successCount: successCount,
          createTime: createTime,
          successTime: successTime,
          showN8Button: false, // AIGC默认不显示N8按钮
          failedReason: state == ExportState.error ? 'AIGC导出过程中发生错误' : null,
        ),
      );
    }

    return exportItems;
  }

  // 更新选中导出类型
  set selectedExportType(ExportType value) {
    _selectedExportType = value;
    notifyListeners();
  }

  void _startPolling() {
    _isPolling = true;
    notifyListeners();

    // 立即执行一次
    _poll();
  }

  Future<void> _poll() async {
    if (!_isPolling || _isDisposed) {
      return;
    }

    try {
      _exportUiStatus = [];
      // 使用新的方法获取包含原始JSON的数据
      final records = await _exportUseCaseProvider.fetchExportReport.invoke();
      _latestRecords = records;
      _isFirstLoaded = true;

      // 直接更新导出状态
      _updateExportStatus(records);

      if (!_isDisposed) {
        notifyListeners();
      }
    } catch (e) {
      PGLog.e('加载导出状态失败: $e');
      _exportUiStatus = [];
      _latestRecords = [];
      if (!_isDisposed) {
        notifyListeners();
      }
    }
  }

  /// 处理导出记录刷新消息
  Future<void> handleReloadExportItems() async {
    PGLog.d('ExportViewModel: 收到刷新导出记录消息');
    await _poll();
  }

  void updateExportRecords(List<ExportRecord> records) {
    if (records.isEmpty) {
      return;
    }

    List<ExportRecord> resultRecords = records.where((record) {
      return !_deletedGuids.contains(record.guid);
    }).toList();

    // 创建一个临时列表存储新的合并记录
    List<ExportRecord> mergedRecords = List.from(_latestRecords);
    bool hasNewRecords = false;

    // 遍历传入的记录
    for (var record in resultRecords) {
      // 查找是否已存在相同 guid 的记录
      final existingIndex =
          mergedRecords.indexWhere((r) => r.guid == record.guid);

      if (existingIndex >= 0) {
        // 如果已存在，更新记录
        mergedRecords[existingIndex] = record;
      } else {
        // 如果不存在，添加新记录
        mergedRecords.add(record);
        hasNewRecords = true;
      }
    }

    // 更新记录列表
    if (hasNewRecords || mergedRecords.length != _latestRecords.length) {
      _latestRecords = mergedRecords;
      PGLog.d('已添加新的导出记录，共 ${_latestRecords.length} 条');
    } else {
      _latestRecords.removeWhere((record) {
        return records.any((r) => r.guid == record.guid);
      });
      _latestRecords.addAll(records);
    }

    // 更新 UI 状态
    _updateExportStatus(resultRecords);
  }

  void _stopPolling() {
    _isPolling = false;
  }

  /// 更新导出状态列表
  /// [records] 最新的导出记录列表
  void _updateExportStatus(List<ExportRecord> records) {
    List<ExportUiStatus> updatedStatus = [];
    final existingStatusMap = {
      for (var status in _exportUiStatus) status.guid: status
    };

    // 获取N8按钮显示权限
    final showN8Button = _canShowN8Button();

    // 处理每个记录
    for (var record in records) {
      if (existingStatusMap.containsKey(record.guid)) {
        // 更新现有状态
        _updateExistingStatus(existingStatusMap[record.guid]!, record);
      } else {
        // 创建新状态，传入N8按钮显示权限
        updatedStatus
            .add(ExportUiStatus.fromRecord(record, showN8Button: showN8Button));
      }
    }

    updatedStatus.sort((a, b) => b.createTime.compareTo(a.createTime));
    _exportUiStatus = updatedStatus;
  }

  /// 更新现有的导出状态对象
  /// [existingStatus] 现有的状态对象
  /// [record] 新的记录数据
  void _updateExistingStatus(
      ExportUiStatus existingStatus, ExportRecord record) {
    final state = ExportState.fromCode(record.exportState);
    // 当 errorNum > 0 时，将状态设置为错误
    // if (record.errorNum != null && record.errorNum! > 0) {
    //   state = ExportState.error;
    // }
    final newProgress = record.successNum / record.itemCount;
    // 更新状态对象的所有属性
    existingStatus
      ..state = state
      ..totalCount = record.itemCount
      ..successCount = record.successNum;

    // 确保进度条更新正确
    if (existingStatus.progress.value != newProgress) {
      existingStatus.progress.value = newProgress;
    }

    // 获取N8按钮显示权限
    final showN8Button = _canShowN8Button();

    existingStatus
      ..successTime =
          ExportUiStatus.fromRecord(record, showN8Button: showN8Button)
              .successTime
      ..failedReason = null;
  }

  /// 根据 guid 查找并更新单个导出状态
  /// [guid] 导出任务的唯一标识
  /// [record] 新的记录数据
  void _updateSingleStatus(String guid, ExportRecord record, bool isNotify) {
    final index = _exportUiStatus.indexWhere((status) => status.guid == guid);
    if (index != -1) {
      _updateExistingStatus(_exportUiStatus[index], record);
      if (!_isDisposed && isNotify) {
        notifyListeners();
      }
    }
  }

  Future<void> onPauseClick(String guid) async {
    PGLog.d("暂停导出点击 $guid");
    try {
      final record = _findRecordByGuid(guid);
      await _exportUseCaseProvider.pauseExportTask.invoke([record]);
      _updateSingleStatus(guid, record, true);
    } catch (e) {
      PGLog.e('暂停导出失败: $e');
    }
  }

  Future<void> onPauseBatchClick(List<String> guids) async {
    PGLog.d("点击批量暂停 $guids");
    try {
      await _exportUseCaseProvider.pauseExportTask.pauseBatchExportTask(guids);
      for (var guid in guids) {
        final record = _findRecordByGuid(guid);
        _updateSingleStatus(guid, record, false);
      }
      notifyListeners();
    } catch (e) {
      PGLog.e('暂停导出失败: $e');
    }
  }

  Future<void> onContinueClick(String guid) async {
    PGLog.d("继续导出点击 $guid");
    try {
      final record = _findRecordByGuid(guid);
      // 等待任务启动完成
      await _exportUseCaseProvider.startExportTask.invoke([record]);
      PGLog.d("启动导出任务完成");
      // 更新状态
    } catch (e) {
      PGLog.e('继续导出失败: $e');
    }
  }

  Future<void> onContinueBatchClick(List<String> guids) async {
    PGLog.d("点击批量导出");
    try {
      await _exportUseCaseProvider.startExportTask
          .continueBatchExportTasks(guids);
      for (var guid in guids) {
        final record = _findRecordByGuid(guid);
        _updateSingleStatus(guid, record, false);
      }
      notifyListeners();
    } catch (e) {
      PGLog.e('批量暂停导出失败: $e');
    }
  }

  Future<void> onSearchClick(String guid) async {
    if (_selectedExportType == ExportType.aigc) {
      await _exportPathViewerHandler.handleAigcExportPathView(guid, null, null);
      return;
    }

    try {
      final record = _findRecordByGuid(guid);
      for (var element in record.exportPaths) {
        final success = await _filePathService.openFolder(element);
        if (success) {
          PGLog.d('打开文件夹成功: $element');
        } else {
          PGLog.e('打开文件夹失败: $element');
        }
      }
    } catch (e) {
      PGLog.e('打开文件夹失败: $e');
    }
  }

  Future<void> onDeleteClick(String guid) async {
    PGLog.d("删除导出点击 $guid");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了删除");
      await _aigcMySampleExportManager.deleteProofingExport([guid]);
      return;
    }
    try {
      final record = _findRecordByGuid(guid);
      await _exportUseCaseProvider.deleteExportTask.invoke([record]);
      _exportUiStatus.removeWhere((v) => v.guid == record.guid);
      _deletedGuids.add(guid);
      notifyListeners();
    } catch (e) {
      PGLog.e('删除导出失败: $e');
    }
  }

  Future<void> onDeleteBatchClick(List<String> guids) async {
    PGLog.d("批量点击删除导出");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了批量删除");
      await _aigcMySampleExportManager.deleteProofingExport(guids);
      return;
    }
    try {
      await _exportUseCaseProvider.deleteExportTask
          .deleteBatchExportTask(guids);
      for (var guid in guids) {
        final record = _findRecordByGuid(guid);
        _exportUiStatus.removeWhere((v) => v.guid == record.guid);
      }
      notifyListeners();
    } catch (e) {
      PGLog.e('删除导出失败: $e');
    }
  }

  Future<void> onRefreshClick(String guid) async {
    PGLog.d("刷新导出点击 $guid");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了刷新");
      PGDialog.showLoading();
      final success = await _aigcMySampleExportManager.retryExport(guid);
      // 关闭loadingg过后马上显示toast。需要await，以免显示不出来
      await PGDialog.dismiss();
      if (success) {
        PGDialog.showToast("重试成功");
      } else {
        PGDialog.showToast("重试失败");
      }
    }
  }

  /// 处理N8排版按钮点击
  Future<void> onN8LayoutClick(String guid) async {
    PGLog.d("N8排版按钮点击 $guid");

    try {
      // 显示加载提示
      PGDialog.showLoading();

      // 查找对应的导出记录
      final record = _findRecordByGuid(guid);

      // 调用N8排版服务
      final result = await N8LayoutService.callN8Layout(record);

      // 关闭加载提示
      await PGDialog.dismiss();

      if (result.success) {
        PGLog.d("N8排版启动成功: $guid");
        PGDialog.showToast("N8排版程序启动成功");
      } else {
        // 显示具体的错误信息
        final errorMessage = result.errorMessage ?? "N8排版启动失败";
        PGDialog.showToast(errorMessage);
        PGLog.e("N8排版启动失败: $guid, 错误: $errorMessage");
      }
    } catch (e) {
      // 关闭加载提示
      await PGDialog.dismiss();
      PGLog.e('N8排版启动失败: $e');
      PGDialog.showToast("N8排版启动失败: $e");
    }
  }

  ExportRecord _findRecordByGuid(String guid) {
    final record = _latestRecords.firstWhere(
      (record) => record.guid == guid,
      orElse: () => throw Exception('未找到对应的导出记录'),
    );
    return record;
  }

  @override
  void dispose() {
    PGLog.d('ExportViewModel dispose');
    _isDisposed = true;
    _stopPolling();
    super.dispose();
  }
}
