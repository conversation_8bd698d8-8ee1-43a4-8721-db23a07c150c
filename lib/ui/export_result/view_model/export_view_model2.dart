import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_models.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/export_records_repository.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/ui/aigc_sample/services/file_path_service.dart';
import 'package:turing_art/ui/export_result/handler/export_path_viewer_handler.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/services/n8_layout_service.dart';
import 'package:turing_art/ui/export_result/use_case/export_use_case_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

class ExportViewModel2 extends ChangeNotifier {
  final ExportUseCaseProvider _exportUseCaseProvider;
  final AigcMySampleExportManager _aigcMySampleExportManager;
  final ExportRecordsRepository _exportRecordsRepository;
  final CurrentUserRepository _currentUserRepository;
  final ExportTaskStateProvider _exportTaskStateProvider;
  final FilePathService _filePathService;
  final ExportPathViewerHandler _exportPathViewerHandler;

  final List<ExportUiStatus> _exportUiStatus = [];
  List<ExportUiStatus> get exportUiStatus => _exportUiStatus;
  // 用于AIGC导出数据
  final List<ExportUiStatus> _aigcItems = [];
  List<ExportUiStatus> get aigcItems => _aigcItems;

  // 导出任务记录缓存
  final List<ExportRecord> _exportTaskRecords = [];

  bool _isDisposed = false;

  bool _isFirstLoaded = false;
  bool get isFirstLoaded => _isFirstLoaded;

  // 导出类型选择相关状态
  ExportType _selectedExportType = ExportType.retouching; // 当前选择的导出类型
  ExportType get selectedExportType => _selectedExportType;

  ExportViewModel2(
    this._exportUseCaseProvider,
    this._aigcMySampleExportManager,
    this._currentUserRepository,
    this._exportRecordsRepository,
    this._exportTaskStateProvider,
    this._filePathService, {
    ExportType? defaultExportType,
  }) : _exportPathViewerHandler = ExportPathViewerHandler(
          _aigcMySampleExportManager,
          _filePathService,
        ) {
    // 如果指定了默认导出类型，则使用它
    if (defaultExportType != null) {
      _selectedExportType = defaultExportType;
    }

    // 设置监听器
    _aigcMySampleExportManager.addListener(_updateAigcData);

    // 监听导出任务状态事件流
    _exportTaskStateProvider.eventStream.listen(_handleExportTaskStateEvent);

    _updateExportData();
    _updateAigcData();
  }

  /// 处理导出任务状态事件
  void _handleExportTaskStateEvent(ExportTaskStateEvent event) {
    if (_isDisposed) {
      return;
    }

    PGLog.d(
        'ExportViewModel2: 收到任务状态事件 - taskIds: ${event.taskIds}, operation: ${event.operationState.name}');

    // 批量处理任务ID的状态变化
    _updateExportRecords(event.taskIds, event.operationState);
  }

  /// 获取是否显示N8按钮的权限
  bool _canShowN8Button() {
    return _currentUserRepository.store?.couldExportSample() ?? false;
  }

  /// 更新AIGC导出数据
  void _updateAigcData() {
    PGLog.d('开始更新AIGC导出数据');

    // 从AigcMySampleExportManager获取真实数据并转换
    final uiModels = _aigcMySampleExportManager.uiModelList;
    final tempAigcItems = _convertAigcUIModelsToExportUiStatus(uiModels);

    // 按创建时间排序
    tempAigcItems.sort((a, b) => b.createTime.compareTo(a.createTime));
    _aigcItems.clear();
    _aigcItems.addAll(tempAigcItems);

    PGLog.d('AIGC导出数据更新完成，共${_aigcItems.length}个项目');

    // 通知UI更新
    if (!_isDisposed) {
      notifyListeners();
    }
  }

  /// 将AigcMySampleExportUIModel转换为ExportUiStatus
  List<ExportUiStatus> _convertAigcUIModelsToExportUiStatus(
      List<AigcMySampleExportUIModel> uiModels) {
    final List<ExportUiStatus> exportItems = [];

    for (final uiModel in uiModels) {
      // 解析导出数量字符串，例如 "3/5"
      final exportNumParts = uiModel.exportNum.split('/');
      final successCount = int.tryParse(exportNumParts[0]) ?? 0;
      final totalCount = int.tryParse(exportNumParts[1]) ?? 0;

      // 根据进度状态转换为ExportState
      ExportState state;
      switch (uiModel.progressStatus) {
        case AigcMySampleExportProgressStatus.processing:
          state = ExportState.processing;
          break;
        case AigcMySampleExportProgressStatus.completed:
          state = ExportState.finish;
          break;
        case AigcMySampleExportProgressStatus.failed:
          state = ExportState.error;
          break;
      }

      final createTime = uiModel.updateTime.millisecondsSinceEpoch;

      // 成功时间（如果已完成）
      final successTime = createTime;

      exportItems.add(
        ExportUiStatus.createAigc(
          guid: uiModel.id, // 使用打样项目ID作为guid
          projectName: uiModel.name,
          state: state,
          totalCount: totalCount,
          successCount: successCount,
          createTime: createTime,
          successTime: successTime,
          showN8Button: false, // AIGC默认不显示N8按钮
          failedReason: state == ExportState.error ? 'AIGC导出过程中发生错误' : null,
        ),
      );
    }

    return exportItems;
  }

  Future<void> _updateExportData() async {
    if (_isDisposed) {
      return;
    }

    try {
      _exportUiStatus.clear();
      _exportTaskRecords.clear();
      // 使用新的方法获取包含原始JSON的数据
      final records = await _exportRecordsRepository.fetchExportRecord();
      _exportTaskRecords.addAll(records);
      _isFirstLoaded = true;

      // 初始化导出状态
      _initExportStatus(records);

      if (!_isDisposed) {
        notifyListeners();
      }
    } catch (e) {
      PGLog.e('加载导出状态失败: $e');
      _exportUiStatus.clear();
      if (!_isDisposed) {
        notifyListeners();
      }
    }
  }

  /// 增量更新导出记录
  /// 批量更新导出记录
  /// [taskIds] 任务ID列表
  /// [operationState] 操作状态
  Future<void> _updateExportRecords(
    List<String> taskIds,
    ExportOperationState operationState,
  ) async {
    if (_isDisposed) {
      return;
    }

    try {
      PGLog.d('ExportViewModel2: 开始批量更新任务 $taskIds, 操作: $operationState');

      // 异步处理，避免UI卡顿
      await Future.microtask(() async {
        switch (operationState) {
          case ExportOperationState.add:
          case ExportOperationState.delete:
            await _updateExportData();
            break;
          case ExportOperationState.update:
            await _handleBatchUpdateOperation(taskIds);
            break;
        }
      });
    } catch (e) {
      PGLog.e('ExportViewModel2: 增量更新失败: $e');
    }
  }

  /// 处理添加操作
  Future<void> _handleAddOperation(String taskId) async {
    try {
      // 获取所有记录并过滤出目标记录
      final records =
          await _exportRecordsRepository.fetchExportRecordsByGuids([taskId]);

      if (records.isNotEmpty) {
        // 检查是否已存在
        final existingIndex =
            _exportTaskRecords.indexWhere((r) => r.guid == taskId);
        if (existingIndex == -1) {
          // 添加新记录
          _exportTaskRecords.addAll(records);

          // 创建新的UI状态
          final showN8Button = _canShowN8Button();
          for (final record in records) {
            final newUiStatus =
                ExportUiStatus.fromRecord(record, showN8Button: showN8Button);
            _exportUiStatus.add(newUiStatus);
          }

          // 重新排序
          _exportUiStatus.sort((a, b) => b.createTime.compareTo(a.createTime));

          PGLog.d('ExportViewModel2: 添加新任务 $taskId');
          if (!_isDisposed) {
            notifyListeners();
          }
        }
      }
    } catch (e) {
      PGLog.e('ExportViewModel2: 处理添加操作失败: $e');
    }
  }

  /// 批量处理更新操作
  Future<void> _handleBatchUpdateOperation(List<String> taskIds) async {
    try {
      // 获取所有记录
      final records =
          await _exportRecordsRepository.fetchExportRecordsByGuids(taskIds);

      if (records.isNotEmpty) {
        bool hasChanges = false;

        for (final record in records) {
          // 更新原始记录
          final recordIndex =
              _exportTaskRecords.indexWhere((r) => r.guid == record.guid);
          if (recordIndex != -1) {
            _exportTaskRecords[recordIndex] = record;

            // 更新UI状态
            final uiIndex =
                _exportUiStatus.indexWhere((s) => s.guid == record.guid);
            if (uiIndex != -1) {
              _updateExistingStatus(_exportUiStatus[uiIndex], record);
              hasChanges = true;
              PGLog.d('ExportViewModel2: 更新任务 ${record.guid}');
            }
          }
        }

        if (hasChanges && !_isDisposed) {
          notifyListeners();
        }
      }
    } catch (e) {
      PGLog.e('ExportViewModel2: 批量处理更新操作失败: $e');
    }
  }

  /// 批量处理删除操作
  Future<void> _handleBatchDeleteOperation(List<String> taskIds) async {
    try {
      bool hasChanges = false;

      for (final taskId in taskIds) {
        // 从原始记录中移除
        final recordCount = _exportTaskRecords.length;
        _exportTaskRecords.removeWhere((r) => r.guid == taskId);
        if (_exportTaskRecords.length < recordCount) {
          hasChanges = true;
        }

        // 从UI状态中移除
        final statusCount = _exportUiStatus.length;
        _exportUiStatus.removeWhere((s) => s.guid == taskId);
        if (_exportUiStatus.length < statusCount) {
          hasChanges = true;
          PGLog.d('ExportViewModel2: 删除任务 $taskId');
        }
      }

      if (hasChanges && !_isDisposed) {
        notifyListeners();
      }
    } catch (e) {
      PGLog.e('ExportViewModel2: 批量处理删除操作失败: $e');
    }
  }

  /// 初始化导出状态列表
  /// [records] 最新的导出记录列表
  void _initExportStatus(List<ExportRecord> records) {
    List<ExportUiStatus> updatedStatus = [];
    final existingStatusMap = {
      for (var status in _exportUiStatus) status.guid: status
    };

    // 获取N8按钮显示权限
    final showN8Button = _canShowN8Button();

    // 处理每个记录
    for (var record in records) {
      if (existingStatusMap.containsKey(record.guid)) {
        // 更新现有状态
        _updateExistingStatus(existingStatusMap[record.guid]!, record);
      } else {
        // 创建新状态，传入N8按钮显示权限
        updatedStatus
            .add(ExportUiStatus.fromRecord(record, showN8Button: showN8Button));
      }
    }

    updatedStatus.sort((a, b) => b.createTime.compareTo(a.createTime));
    PGLog.d('更新导出状态列表，共${updatedStatus.length}个项目');
    _exportUiStatus.clear();
    _exportUiStatus.addAll(updatedStatus);
  }

  ExportRecord _findRecordByGuid(String guid) {
    return _exportTaskRecords.firstWhere(
      (record) => record.guid == guid,
      orElse: () => throw Exception('未找到对应的导出记录'),
    );
  }

  /// 更新现有的导出状态对象
  /// [existingStatus] 现有的状态对象
  /// [record] 新的记录数据
  void _updateExistingStatus(
      ExportUiStatus existingStatus, ExportRecord record) {
    final state = ExportState.fromCode(record.exportState);
    // 当 errorNum > 0 时，将状态设置为错误
    // if (record.errorNum != null && record.errorNum! > 0) {
    //   state = ExportState.error;
    // }
    final newProgress = record.successNum / record.itemCount;
    // 更新状态对象的所有属性
    existingStatus
      ..state = state
      ..totalCount = record.itemCount
      ..successCount = record.successNum;

    // 确保进度条更新正确
    if (existingStatus.progress.value != newProgress) {
      existingStatus.progress.value = newProgress;
    }

    // 获取N8按钮显示权限
    final showN8Button = _canShowN8Button();

    existingStatus
      ..successTime =
          ExportUiStatus.fromRecord(record, showN8Button: showN8Button)
              .successTime
      ..failedReason = null;
  }

  /// 根据 guid 查找并更新单个导出状态
  /// [guid] 导出任务的唯一标识
  /// [record] 新的记录数据
  void _updateSingleStatus(String guid, ExportRecord record, bool isNotify) {
    final index = _exportUiStatus.indexWhere((status) => status.guid == guid);
    if (index != -1) {
      _updateExistingStatus(_exportUiStatus[index], record);
      if (!_isDisposed && isNotify) {
        notifyListeners();
      }
    }
  }

  // 更新选中导出类型
  set selectedExportType(ExportType value) {
    _selectedExportType = value;
    notifyListeners();
  }

  Future<void> onPauseClick(String guid) async {
    PGLog.d("暂停导出点击 $guid");
    try {
      final record = _findRecordByGuid(guid);
      await _exportUseCaseProvider.pauseExportTask.invoke([record]);
      _updateSingleStatus(guid, record, true);
    } catch (e) {
      PGLog.e('暂停导出失败: $e');
    }
  }

  Future<void> onPauseBatchClick(List<String> guids) async {
    PGLog.d("点击批量暂停 $guids");
    try {
      await _exportUseCaseProvider.pauseExportTask.pauseBatchExportTask(guids);
      for (var guid in guids) {
        final record = _findRecordByGuid(guid);
        _updateSingleStatus(guid, record, false);
      }
      notifyListeners();
    } catch (e) {
      PGLog.e('暂停导出失败: $e');
    }
  }

  Future<void> onContinueClick(String guid) async {
    PGLog.d("继续导出点击 $guid");
    try {
      final record = _findRecordByGuid(guid);
      // 等待任务启动完成
      await _exportUseCaseProvider.startExportTask.invoke([record]);
      PGLog.d("启动导出任务完成");
      // 更新状态
    } catch (e) {
      PGLog.e('继续导出失败: $e');
    }
  }

  Future<void> onContinueBatchClick(List<String> guids) async {
    PGLog.d("点击批量导出");
    try {
      await _exportUseCaseProvider.startExportTask
          .continueBatchExportTasks(guids);
      for (var guid in guids) {
        final record = _findRecordByGuid(guid);
        _updateSingleStatus(guid, record, false);
      }
      notifyListeners();
    } catch (e) {
      PGLog.e('批量暂停导出失败: $e');
    }
  }

  Future<void> onSearchClick(String guid) async {
    if (_selectedExportType == ExportType.aigc) {
      await _exportPathViewerHandler.handleAigcExportPathView(guid, null, null);
      return;
    }

    try {
      final record = _findRecordByGuid(guid);
      for (var element in record.exportPaths) {
        final success = await _filePathService.openFolder(element);
        if (success) {
          PGLog.d('打开文件夹成功: $element');
        } else {
          PGLog.e('打开文件夹失败: $element');
        }
      }
    } catch (e) {
      PGLog.e('打开文件夹失败: $e');
    }
  }

  Future<void> onDeleteClick(String guid) async {
    PGLog.d("删除导出点击 $guid");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了删除");
      await _aigcMySampleExportManager.deleteProofingExport([guid]);
      return;
    }
    try {
      final record = _findRecordByGuid(guid);
      await _exportUseCaseProvider.deleteExportTask.invoke([record]);
      _handleBatchDeleteOperation([guid]);
      notifyListeners();
    } catch (e) {
      PGLog.e('删除导出失败: $e');
    }
  }

  Future<void> onDeleteBatchClick(List<String> guids) async {
    PGLog.d("批量点击删除导出");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了批量删除");
      await _aigcMySampleExportManager.deleteProofingExport(guids);
      return;
    }
    try {
      await _exportUseCaseProvider.deleteExportTask
          .deleteBatchExportTask(guids);
      _handleBatchDeleteOperation(guids);
      notifyListeners();
    } catch (e) {
      PGLog.e('删除导出失败: $e');
    }
  }

  Future<void> onRefreshClick(String guid) async {
    PGLog.d("刷新导出点击 $guid");
    if (_selectedExportType == ExportType.aigc) {
      PGLog.d("AIGC导出点击了刷新");
      PGDialog.showLoading();
      final success = await _aigcMySampleExportManager.retryExport(guid);
      // 关闭loadingg过后马上显示toast。需要await，以免显示不出来
      await PGDialog.dismiss();
      if (success) {
        PGDialog.showToast("重试成功");
      } else {
        PGDialog.showToast("重试失败");
      }
    }
  }

  /// 处理N8排版按钮点击
  Future<void> onN8LayoutClick(String guid) async {
    PGLog.d("N8排版按钮点击 $guid");

    try {
      // 显示加载提示
      PGDialog.showLoading();

      // 查找对应的导出记录
      final record = _findRecordByGuid(guid);

      // 调用N8排版服务
      final result = await N8LayoutService.callN8Layout(record);

      // 关闭加载提示
      await PGDialog.dismiss();

      if (result.success) {
        PGLog.d("N8排版启动成功: $guid");
        PGDialog.showToast("N8排版程序启动成功");
      } else {
        // 显示具体的错误信息
        final errorMessage = result.errorMessage ?? "N8排版启动失败";
        PGDialog.showToast(errorMessage);
        PGLog.e("N8排版启动失败: $guid, 错误: $errorMessage");
      }
    } catch (e) {
      // 关闭加载提示
      await PGDialog.dismiss();
      PGLog.e('N8排版启动失败: $e');
      PGDialog.showToast("N8排版启动失败: $e");
    }
  }

  @override
  void dispose() {
    PGLog.d('ExportViewModel dispose');
    _isDisposed = true;
    // 移除监听器，防止内存泄漏
    _aigcMySampleExportManager.removeListener(_updateAigcData);
    super.dispose();
  }
}
