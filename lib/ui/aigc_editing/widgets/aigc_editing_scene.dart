import 'dart:io';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:path/path.dart' as path;
import 'package:provider/provider.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_effect.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/cache_first_editing_project_repository_impl.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/database/operater/db_operater.dart';
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/routing/routes.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_preferred_mask_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_multi_select_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_mask_query_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_mask_acquisition_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_painter_path_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/ui/aigc_editing/services/aigc_editing_service_coordinator.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_operation_view_model.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_scene_view_model.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_thumbnail_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_icon_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_sample_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_eidt_top_bar_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_theme_image_preview_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_editing_canvas_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/control/aigc_editing_operation_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_list_widget.dart';
import 'package:turing_art/ui/common/drag_event_handler/widget/route_aware_drag_handler_widget.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/export_result/model/export_ui_status.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/utils/app_constants.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

// AIGC编辑主场景Windows端
class AigcEditingScene extends StatefulWidget {
  final String? projectId;
  final bool enableTestMode; // 添加测试模式开关

  const AigcEditingScene({
    super.key,
    required this.projectId,
    this.enableTestMode = true, // 默认关闭测试模式
  });

  @override
  State<AigcEditingScene> createState() => _AigcEditingSceneState();
}

class _AigcEditingSceneState extends State<AigcEditingScene> {
  // 用户积分，实际使用时应该从数据源获取
  final int _score = 9999500;

  // 当前预览的创意项
  AigcPresetsEffect? _previewCreativeItem;

  // 是否显示预览
  bool _showPreview = false;

  // 在 _AigcEditingSceneState 类中添加一个成员变量
  AigcEditingOperationViewModel? _operationViewModel;

  // 标记是否已初始化
  bool _isOperationViewModelInitialized = false;

  late AigcEditingThemeListProvider _themeListProvider;

  // 场景ViewModel引用，用于在dispose时取消批量打样
  AigcEditingSceneViewModel? _sceneViewModel;

  @override
  void initState() {
    super.initState();
  }

  /// 初始化操作视图模型
  void _initializeOperationViewModel(
      BuildContext context,
      AigcPresetsRepository aigcPresetsRepository,
      AigcEditingSceneViewModel viewModel) {
    if (_isOperationViewModelInitialized) {
      return;
    }
    _themeListProvider = context.read<AigcEditingThemeListProvider>();
    _themeListProvider.addListener(_onListenerPreviewImageHoverChanged);
    // 初始化操作视图模型
    _operationViewModel = AigcEditingOperationViewModel(
      aigcPresetsRepository: aigcPresetsRepository,
      accountRepository: context.read<AccountRepository>(),
      sceneViewModel: viewModel,
      themeListProvider: _themeListProvider,
      imageProvider: context.read<AigcEditingImageProvider>(),
      multiSelectProvider: context.read<AigcEditingMultiSelectProvider>(),
      // 传入场景ViewModel
      onCreateThemeClicked: () {
        // 处理创建主题点击
        _toCreatePreset(context);
      },
    );

    _isOperationViewModelInitialized = true;
  }

  @override
  void dispose() {
    // 取消正在进行的单个打样操作，避免在页面销毁后显示提示
    _operationViewModel?.cancelCurrentOperation();

    // 取消正在进行的打样操作（单个打样和批量打样通用）
    _sceneViewModel?.cancelSample();

    // 释放资源
    _operationViewModel?.dispose();
    // 在下一帧回调中安全释放，避免在组件树不稳定时操作
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _canvasViewModel.dispose();
    // });
    _themeListProvider.removeListener(_onListenerPreviewImageHoverChanged);

    // 释放所有Toast，确保页面退出时不会有残留的提示
    AigcEditIconToast.dismissIconToast();
    AigcEditSampleToast.dismissSampleToast();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String projectId = widget.projectId ?? '';
    if (projectId.isEmpty) {
      return const Center(child: Text('项目ID不能为空'));
    }
    return MultiProvider(
      providers: [
        Provider<CurrentEditingProjectRepository>(
          create: (context) {
            final repository = CacheFirstEditingProjectRepositoryImpl(
              DbOperater(),
            );
            repository.switchWorkspace(projectId);
            return repository;
          },
        ),
        ChangeNotifierProvider(
          create: (context) => AigcEditingHistoryProvider(
            dbOperater: DbOperater(),
          ),
        ),
        // 首先提供共享数据Provider
        ChangeNotifierProvider(
          create: (context) {
            return AigcEditingImageProvider();
          },
        ),
        ChangeNotifierProvider(
          create: (context) => AigcEditingControlProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AigcEditingThemeListProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AigcCanvasPainterProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AigcPainterPathProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AigcRegionalFrameProvider(),
        ),
        Provider(
          create: (context) => AigcPreviewImageItemAdapter(
            context.read<MediaRepository>(),
          ),
        ),

        ChangeNotifierProvider(
          create: (context) => AigcEditingMultiSelectProvider(),
        ),
        // 添加服务协调器Provider
        ChangeNotifierProvider(
          create: (context) => AigcEditingServiceCoordinator(
            imageProvider: context.read<AigcEditingImageProvider>(),
            multiSelectProvider: context.read<AigcEditingMultiSelectProvider>(),
            aigcService: context.read<AigcService>(),
            currentEditingProjectRepository:
                context.read<CurrentEditingProjectRepository>(),
            mediaRepository: context.read<MediaRepository>(),
            maskQueryProvider: AigcImageMaskQueryProvider(
              historyProvider: context.read<AigcEditingHistoryProvider>(),
            ),
            maskAcquisitionProvider:
                context.read<AigcMaskAcquisitionProvider>(),
            aigcPreviewImageItemAdapter:
                context.read<AigcPreviewImageItemAdapter>(),
          ),
          lazy: false, // 强制立即初始化，因为这是后台服务
        ),
        Provider(
          create: (context) => AigcEditingImagePreferredMaskProvider(
            historyProvider: context.read<AigcEditingHistoryProvider>(),
            currentEditingProjectRepository:
                context.read<CurrentEditingProjectRepository>(),
            imageProvider: context.read<AigcEditingImageProvider>(),
          ),
        ),
        ChangeNotifierProvider(
          create: (context) {
            final imageProvider = context.read<AigcEditingImageProvider>();
            final currentProjectRepository =
                context.read<CurrentEditingProjectRepository>();
            final canvasPainterProvider =
                context.read<AigcCanvasPainterProvider>();

            return AigcImageOverlayProvider(
              imageProvider: imageProvider,
              currentProjectRepository: currentProjectRepository,
              canvasPainterProvider: canvasPainterProvider,
              themeListProvider: context.read<AigcEditingThemeListProvider>(),
              controlProvider: context.read<AigcEditingControlProvider>(),
              preferredMaskProvider:
                  context.read<AigcEditingImagePreferredMaskProvider>(),
            );
          },
        ),

        // 然后提供Scene ViewModel，注入共享Provider
        ChangeNotifierProvider(
          create: (context) => AigcEditingSceneViewModel(
              mediaUploadRepository: context.read<MediaUploadRepository>(),
              aigcSampleRepository: context.read<AigcSampleRepository>(),
              currentEditingProjectRepository:
                  context.read<CurrentEditingProjectRepository>(),
              navigator: GoRouterNavigatorService(context),
              projectStateProvider: context.read<ProjectStateProvider>(),
              imageProvider: context.read<AigcEditingImageProvider>(),
              themeListProvider: context.read<AigcEditingThemeListProvider>(),
              mediaRepository: context.read<MediaRepository>(),
              aigcEditingHistoryProvider:
                  context.read<AigcEditingHistoryProvider>(),
              aigcRegionalFrameProvider:
                  context.read<AigcRegionalFrameProvider>(),
              preferredMaskProvider:
                  context.read<AigcEditingImagePreferredMaskProvider>(),
              customTableRepository: context.read<OpsCustomTableRepository>()),
        ),
        ChangeNotifierProvider(
          create: (context) => AigcThumbnailListViewModel.create(context),
        ),
      ],
      child: _buildScene(),
    );
  }

  Widget _buildScene() {
    return Consumer<AigcEditingSceneViewModel>(
      builder: (context, viewModel, child) {
        // 保存场景ViewModel引用，用于在dispose时取消批量打样
        _sceneViewModel = viewModel;

        // 从上下文获取AigcPresetsRepository并初始化OperationViewModel
        final aigcPresetsRepository = context.read<AigcPresetsRepository>();

        // 确保OperationViewModel已初始化
        if (!_isOperationViewModelInitialized) {
          _initializeOperationViewModel(
              context, aigcPresetsRepository, viewModel);
        }

        // 如果OperationViewModel还未准备好，显示加载状态
        if (_operationViewModel == null) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22EDFF)),
            ),
          );
        }

        // 移除手动状态同步逻辑，因为现在使用共享Provider自动同步

        if (AppConstants.isDesktop) {
          return _buildDesktopLayout(viewModel);
        } else {
          return _buildMobileLayout(viewModel);
        }
      },
    );
  }

  Widget _buildDesktopLayout(AigcEditingSceneViewModel viewModel) {
    return TitleBarWidget(
      backgroundColor: const Color(0xFF0D0D0D),
      funcWidget: AigcEditTopBarWidget(
        score: _score,
        backgroundColor: const Color(0xFF0D0D0D),
        showPurchaseButton: false, // 隐藏积分购买按钮
        onBackPressed: viewModel.onBackPressed,
        onPurchasePressed: () {
          // 处理购买点击事件
        },
        onPresetPressed: () {
          // 处理预设点击事件
          if (viewModel.isCreatingSample) {
            return;
          }
          final navigator = GoRouterNavigatorService(context);
          navigator.navigateToAigcPresets(
            maskPath: '',
            previewPath: '',
          );
        },
        onExportPressed: () {
          // 处理导出点击事件，默认选中AIGC导出
          if (viewModel.isCreatingSample) {
            return;
          }
          ExportListDialog2.show(defaultExportType: ExportType.aigc);
        },
        onMakingCenterPressed: () {
          // 处理打样中心点击事件
          if (viewModel.isCreatingSample) {
            return;
          }
          context.push(Routes.aigcSample);
        },
      ),
      child: RouteAwareDragHandlerWidget(
        canProcessFiles: () => true, // AIGC编辑页面总是可以处理文件
        showDragForbiddenOverlay: true,
        onDragDone: (result) async {
          // 处理拖拽完成，result是DealImageFilesResult类型
          if (result.validFiles.isNotEmpty) {
            PGDialog.showLoading();
            PGLog.d('AIGC编辑页面 - 拖拽文件: ${result.validFiles.length} 个有效文件');
            final success = await viewModel.onDragDone(result);
            await PGDialog.dismiss();
            if (!success) {
              PGDialog.showToast('操作失败');
            } else {
              PGDialog.showToast('本次共导入${result.validFiles.length}张照片');
            }
          }
        },
        child: Container(
          color: const Color(0xFF0D0D0D),
          child: Stack(
            children: [
              // 编辑画布
              const Positioned(
                top: 0,
                left: 0,
                right: 285,
                bottom: 124,
                child: AigcEditingCanvasWidget(
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),

              // 右侧操作面板
              _buildOperationPanel(),

              // 图片预览组件
              _buildImagePreview(),

              // 缩略图列表区域
              _buildThumbnailArea(viewModel),

              // 全屏事件阻止遮罩
              // if (viewModel.isCreatingSample)
              //   Positioned.fill(
              //     child: AbsorbPointer(
              //       child: Container(
              //         color: Colors.transparent,
              //       ),
              //     ),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(AigcEditingSceneViewModel viewModel) {
    return Container(
      color: const Color(0xFF0D0D0D),
      child: Column(
        children: [
          // 顶部导航栏
          AigcEditTopBarWidget(
            score: _score,
            showPurchaseButton: false, // 隐藏积分购买按钮
            onBackPressed: viewModel.onBackPressed,
            onPurchasePressed: () {
              // 处理购买点击事件
            },
            onPresetPressed: () {
              // 跳转到预设页面并携带参数
              final navigator = GoRouterNavigatorService(context);
              navigator.navigateToAigcPresets(
                maskPath: '',
                previewPath: '',
              );
            },
            onExportPressed: () {
              // 处理导出点击事件，默认选中AIGC导出
              ExportListDialog2.show(defaultExportType: ExportType.aigc);
            },
            onMakingCenterPressed: () {
              // 处理打样中心点击事件
              final navigator = GoRouterNavigatorService(context);
              navigator.smartPush(Routes.aigcSample);
            },
          ),

          // 内容区域
          Expanded(
            child: Stack(
              children: [
                // 编辑画布
                const Positioned(
                  top: 0,
                  left: 0,
                  right: 285,
                  bottom: 124,
                  child: AigcEditingCanvasWidget(
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),

                // 右侧操作面板
                _buildOperationPanel(),

                // 图片预览组件
                _buildImagePreview(),

                // 缩略图列表区域
                _buildThumbnailArea(viewModel),

                // 全屏事件阻止遮罩
                //   if (viewModel.isCreatingSample)
                //     Positioned.fill(
                //       child: AbsorbPointer(
                //         child: Container(
                //           color: Colors.transparent,
                //         ),
                //       ),
                //     ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onListenerPreviewImageHoverChanged() {
    // 处理主题变更
    if (_themeListProvider.hoverEffect != null) {
      setState(() {
        _previewCreativeItem = _themeListProvider.hoverEffect;
        _showPreview = true;
      });
    } else {
      setState(() {
        _showPreview = false;
      });
    }
  }

  /// 构建操作面板
  Widget _buildOperationPanel() {
    return Positioned(
      top: 0,
      right: 0,
      bottom: 0,
      width: 285,
      child: Consumer<AigcEditingSceneViewModel>(
        builder: (context, sceneViewModel, child) {
          return AigcEditingOperationWidget(
            viewModel: _operationViewModel!,
            onBatchSample: sceneViewModel.createBatchSamples,
          );
        },
      ),
    );
  }

  /// 构建图片预览组件
  Widget _buildImagePreview() {
    if (!_showPreview || _previewCreativeItem == null) {
      return const SizedBox.shrink();
    }

    return Consumer<AigcCanvasPainterProvider>(
      builder: (context, canvasPainterProvider, child) {
        return Positioned(
          top: AppConstants.isDesktop ? 96 : 90,
          right: 285 + 4,
          child: AigcThemeImagePreviewWidget(
            creativeItem: _previewCreativeItem!,
            visible: _showPreview,
            onMouseExit: () {
              setState(() {
                _showPreview = false;
              });
            },
          ),
        );
      },
    );
  }

  /// 构建缩略图列表区域
  Widget _buildThumbnailArea(AigcEditingSceneViewModel viewModel) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 285,
      height: 124,
      child: Container(
          color: const Color(0xFF1F1F1F),
          child: const AigcThumbnailListWidget(
            // 不再直接传递viewModel，子组件将通过context.read<ThumbnailLoadManager>()获取
            height: 124,
          )),
    );
  }

  void _toCreatePreset(BuildContext context) async {
    final editingImageProvider = context.read<AigcEditingImageProvider>();
    final imageData = editingImageProvider.selectedImage;

    if (imageData == null) {
      PGLog.e('点击创建预设时获取当前 AigcPreviewImageItem 为空');
      return;
    }

    final preferredMaskProvider =
        context.read<AigcEditingImagePreferredMaskProvider>();

    final maskPath = preferredMaskProvider.currentImagePreferredMaskPath;
    final previewPath = imageData.previewPath;
    if (previewPath == null || previewPath.isEmpty) {
      if (mounted) {
        PGDialog.showToast('预览图还未生成');
      }
      return;
    }
    if (maskPath == null || maskPath.isEmpty) {
      if (mounted) {
        PGDialog.showToast('主体保护区域生成中，请稍后再进行创建');
      }
      return;
    }

    // 在异步调用之前保存navigator
    final navigator = GoRouterNavigatorService(context);

    // 处理遮罩图像
    String? processedMaskPath;
    try {
      processedMaskPath = await _processMaskImage(maskPath);
    } catch (e) {
      PGLog.e('处理遮罩图失败: $e');
      if (mounted) {
        PGDialog.showToast('处理遮罩图失败');
      }
      return;
    }

    // 检查widget是否还处于挂载状态
    if (!mounted) {
      return;
    }

    // 跳转到预设页面并携带参数
    navigator.navigateToAigcPresets(
      maskPath: processedMaskPath ?? maskPath,
      previewPath: previewPath,
    );
  }

  /// 处理遮罩图像
  /// 1. 检查路径是否为空，为空则跳过
  /// 2. 检查同级目录下的uploadMask.png文件，存在则删除
  /// 3. 使用convertRGBAFromR方法处理图像并保存为uploadMask.png
  /// 4. 返回处理后的文件路径
  Future<String?> _processMaskImage(String? maskPath) async {
    try {
      // 1. 如果遮罩路径为空则跳过
      if (maskPath == null || maskPath.isEmpty) {
        PGLog.d('遮罩图路径为空，跳过处理');
        return null;
      }

      // 检查原始遮罩文件是否存在
      final maskFile = File(maskPath);
      if (!maskFile.existsSync()) {
        PGLog.e('遮罩图文件不存在: $maskPath');
        return null;
      }

      // 2. 获取同级目录并检查uploadMask.png文件
      final maskDirectory = maskFile.parent;
      final uploadMaskPath = path.join(
          maskDirectory.path, MediaResourceConstants.getPsUploadMaskFileName());
      final uploadMaskFile = File(uploadMaskPath);

      // 如果uploadMask.png存在则删除
      if (uploadMaskFile.existsSync()) {
        await uploadMaskFile.delete();
        PGLog.d('删除已存在的uploadMask.png文件: $uploadMaskPath');
      }

      // 3. 使用processFile方法处理图像，最大边缩放到1440的PNG
      const config = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcPreviewSize,
        targetHeight: ImageConstants.aigcPreviewSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'png',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.initialize();

      await ImageProcessorService.processFile(
        maskPath,
        uploadMaskPath,
        config: config,
      );

      PGLog.d('遮罩图处理成功: $uploadMaskPath');
      return uploadMaskPath;
    } catch (e) {
      PGLog.e('处理遮罩图失败: $e');
      return null;
    }
  }
}
