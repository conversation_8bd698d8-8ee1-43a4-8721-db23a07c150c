import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/providers/export_task_state_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/project_home/view_models/preload_unity_view_model.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/unity/widgets/turing_unity_widget.dart';

class PreloadUnityWidget extends StatelessWidget {
  const PreloadUnityWidget({super.key});
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => PreloadUnityViewModel(
        useCaseProvider: context.read<UnityUseCaseProvider>(),
        unityController: context.read<UnityController>(),
        exportUseCaseProvider: context.read<ExportUseCaseProvider>(),
        settingRepository: context.read<SettingRepository>(),
        currentDeviceInformationProvider:
            context.read<CurrentDeviceInformationProvider>(),
        noviceGuideManager: context.read<NoviceGuideManager>(),
        exportProjectProvider: context.read<ExportProjectProvider>(),
        exportTaskStateProvider: context.read<ExportTaskStateProvider>(),
      ),
      child: Consumer<PreloadUnityViewModel>(
        builder: (context, viewModel, child) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 暂时不需要实时打印unity消息
              SizedBox(
                width: 10,
                height: 10,
                child: Offstage(
                  offstage: true,
                  child: TuringUnityWidget(
                    onCreatedCallback: (sender) {
                      viewModel.onUnityCreated();
                    },
                    onUnityMessageCallback: (message) {
                      viewModel.onUnityMessage(message);
                    },
                    onDebugMessageCallback: (message) {
                      viewModel.updateLatestMessage(message);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
