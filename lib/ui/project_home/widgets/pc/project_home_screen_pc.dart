import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pg_turing_collect_event/collect/pay_action_log.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:turing_art/constants/sentry_constants.dart';
import 'package:turing_art/core/manager/device_rating_manager.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/enums/order_status.dart';
import 'package:turing_art/datalayer/domain/events/order_event.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/purchase_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/routing/routes.dart';
import 'package:turing_art/ui/common/drag_event_handler/widget/route_aware_drag_handler_widget.dart';
import 'package:turing_art/ui/common/global_vars.dart';
import 'package:turing_art/ui/common/mouse_event_handler/mouse_event_handler_widget.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog.dart';
import 'package:turing_art/ui/export_result/widgets/export_list_dialog2.dart';
import 'package:turing_art/ui/new_user_benefits/widgets/new_uer_benefit_dialog.dart';
import 'package:turing_art/ui/preset/widget/preset_dialog.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/services/novice_guide_ui_service.dart';
import 'package:turing_art/ui/project_home/services/profile_view_click_service.dart';
import 'package:turing_art/ui/project_home/view_models/home_view_model.dart';
import 'package:turing_art/ui/project_home/view_models/novice_guide_view_model.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_empty_state.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_muti_choice_top_bar.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_grid_view.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_header.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_profile_view.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_sort_dialog.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_top_bar.dart';
import 'package:turing_art/ui/project_home/widgets/project_home_title_bar_option.dart';
import 'package:turing_art/ui/project_home/widgets/unity/preload_unity_widget.dart';
import 'package:turing_art/ui/purchase/widgets/purchase_success_dialog.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/ui/version_update/widgets/version_update_dialog.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/update_manager.dart';

/// 项目主页面
class ProjectHomeScreenPC extends StatefulWidget {
  const ProjectHomeScreenPC({
    super.key,
    this.title = 'ProjectHomeScreenPC',
  });

  final String title;

  @override
  State<ProjectHomeScreenPC> createState() => _ProjectHomeScreenPCState();
}

class _ProjectHomeScreenPCState extends State<ProjectHomeScreenPC>
    implements AccountChangeListener {
  final double appBarHeight = 100 + 12 + 56;
  final _topBarKey = GlobalKey<ProjectHomePCTopBarState>();
  final _titleBarOptionKey = GlobalKey();
  final ScrollController _scrollController = ScrollController();
  static const headerHeight = 112.0;
  // Header是否可见，用于渐隐渐显动画
  bool _isHeaderVisible = true;
  // Header是否显示，用于切换批处理模式
  bool _showHeader = true;
  // 是否显示创建项目按钮
  bool _showCreateProjectOnly = false; // 确保有初始值
  // 是否是普通模式， 批量处理不是普通模式
  bool _isNormalMode = true;
  // 是否显示Unity启动加载
  bool showLoadingForUnityLaunch = false;
  final _profileViewKey = GlobalKey();
  final _userCardKey = GlobalKey();

  // 缓存HomeViewModel，避免在监听器中使用Provider
  HomeViewModel? _cachedHomeViewModel;
  ProfileDialogViewModel? _cachedProfileViewModel;
  NoviceGuideDemoProjectViewModel? _cachedNoviceGuideFirstStepViewModel;

  // 缓存屏幕宽度，避免不必要的重新计算
  double? _cachedScreenWidth;

  bool _isLoading = false;

  // 订阅订单状态变更事件
  late StreamSubscription<OrderEvent> _orderEventSubscription;

  @override
  void initState() {
    super.initState();
    PGLog.d('ProjectHomeScreenPC - initState');
    _scrollController.addListener(_handleScroll);

    // 订阅订单状态变更事件
    _orderEventSubscription = context
        .read<PurchaseStateProvider>()
        .orderStatusEvents
        .listen(_handleOrderStatusChangeEvent);

    // 使用Future.microtask来避免在initState中直接调用异步方法
    Future.microtask(() async {
      await UpdateManager().checkUpdateVersion(isLaunch: true).then((result) {
        if (result.isUpdateVersion != null &&
            result.isUpdateVersion == true &&
            mounted) {
          VersionUpdateDialog.show(context);
        }
      });
    });

    // 初次加载时执行一次检查，在UI构建完成后执行初始化检查(保障model有值)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          if (_cachedProfileViewModel != null) {
            _showWechatGiftDialogIfNeeded(_cachedProfileViewModel!);
            _showAddWechatToastIfNeeded(_cachedProfileViewModel!);
            _checkAndGetNewUserBenefits(_cachedProfileViewModel!);
          }

          _checkScrollPositionAndUpdateState();

          // 如果HomeViewModel已初始化，手动初始化NoviceGuideViewModel
          if (_cachedHomeViewModel != null) {
            _initNoviceModelAndStartHandNovice();
          }
        } catch (e) {
          PGLog.e('初始化检查失败: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    // 移除订单状态监听
    _orderEventSubscription.cancel();
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();

    PGLog.d('ProjectHomeScreenPC - dispose');
    super.dispose();
  }

  // 实现AccountChangeListener接口
  @override
  void onAccountChange(ProfileDialogViewModel viewModel) {
    PGLog.d('ProjectHomeScreenPC - onAccountChang 收到通知');
    Sentry.configureScope((scope) => scope.setTag(
        SentryConstants.tagUserMobile, viewModel.mobile ?? 'unknown'));

    // 用户登录后，再次发送设备评级信息，这次会包含用户手机号标签
    if (DeviceRatingManager.ratingResult != null) {
      final deviceRatingMessage = DeviceRatingManager.getDetailedSummary();
      Sentry.captureMessage(deviceRatingMessage, level: SentryLevel.info);
      PGLog.i('用户登录后发送设备评级信息: $deviceRatingMessage');
    }

    if (viewModel.getNeedShowWechatGiftDialog()) {
      // 检查弹窗状态
      _showWechatGiftDialogIfNeeded(viewModel);
      // 检查微信福利添加状态
      _showAddWechatToastIfNeeded(viewModel);
    } else {
      PGLog.d('ProjectHomeScreenPC - onAccountChang 不需要展示微信礼包弹窗');
    }
  }

  // 监听滚动，处理相关状态
  void _handleScroll() {
    if (!mounted) {
      PGLog.d('mounted == false');
      return;
    }
    final offset = _scrollController.offset;
    bool shouldShowCreateProjectOnly = offset >= headerHeight;
    // 如果当前未展示header，说明是在批处理模式，此时不需要显示创建项目按钮
    if (_isNormalMode == false) {
      shouldShowCreateProjectOnly = false;
      return;
    }
    if (shouldShowCreateProjectOnly != _showCreateProjectOnly) {
      setState(() {
        _showCreateProjectOnly = shouldShowCreateProjectOnly;
      });
    }
  }

  // 处理批量处理，主要包括相关状态、动画及偏移量等
  void _handleBatchProcess(HomeViewModel viewModel, {int? index}) async {
    // 如果没有项目，直接返回
    if (viewModel.projects.isEmpty) {
      PGDialog.showToast('没有项目，无法使用批量修图');
      return;
    }
    _isNormalMode = false;
    // 1. 进入批量处理模式
    viewModel.batchProcess();

    if (index != null && index >= 0) {
      viewModel.selectedProject(index);
    }

    // 2. 开始渐隐动画
    setState(() {
      _isHeaderVisible = false;
    });

    // 3. 执行滚动动画，但先检查内容高度是否足够
    bool didScroll = false;
    if (_scrollController.position.maxScrollExtent >= headerHeight) {
      didScroll = true;
      await _scrollController.animateTo(
        headerHeight, // header的高度
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    // 如果内容高度不足，则跳过滚动动画

    // 4. 等待渐隐动画完成后移除 header
    // 只有在执行了滚动动画时才需要等待
    if (didScroll) {
      await Future.delayed(const Duration(milliseconds: 300));
    } else {
      // 如果没有滚动，仍需要短暂延迟以便渐隐动画有效果
      // await Future.delayed(const Duration(milliseconds: 50));
    }
    _scrollController.jumpTo(0.0);
    // 5. 暂时移除滚动监听，避免状态冲突
    _scrollController.removeListener(_handleScroll);

    setState(() {
      _showHeader = false;
    });
    // 7. 恢复滚动监听
    _scrollController.addListener(_handleScroll);
  }

  // 处理批量处理结束，主要包括相关状态、动画及偏移量等
  void _handleBatchProcessEnd(HomeViewModel viewModel) async {
    // 1. 退出批量处理模式
    viewModel.batchProcessEnd();

    // 2. 暂时移除滚动监听，避免状态冲突
    _scrollController.removeListener(_handleScroll);

    // 3. 先添加 header（但保持透明）并设置初始偏移量
    setState(() {
      _showHeader = true;
      _isHeaderVisible = false;
    });

    // 4. 等待一帧确保 header 已添加到布局中
    await Future.delayed(Duration.zero);

    // 5. 检查是否需要处理滚动位置
    // 先将滚动位置设置到header高度
    _scrollController.jumpTo(headerHeight);

    // 6. 开始渐显动画和滚动动画
    setState(() {
      _isHeaderVisible = true;
    });

    await _scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // 7. 等待动画完成
    // 只有在执行了滚动动画时才需要等待完整时间
    await Future.delayed(const Duration(milliseconds: 300));

    // 8. 恢复正常模式和滚动监听
    _isNormalMode = true;
    _scrollController.addListener(_handleScroll);
  }

  // 处理订单状态变更事件(目前只是toast，以后应该是触发权益获取的逻辑，再通过consumer触发具体UI更新)
  void _handleOrderStatusChangeEvent(OrderEvent event) {
    if (!mounted) {
      PGLog.d('mounted == false');
      return;
    }
    PGLog.d(
        'ProjectHomeScreenPC - _handleOrderStatusChangeEvent ${event.status}');
    PGLog.d(
        'ProjectHomeScreenPC - _handleOrderStatusChangeEvent ${event.purchasePlanId}');
    // 上报订单结果埋点
    _reportPayResult(event);
    // 处理订单完成事件
    if (event.status == OrderStatus.completed) {
      // 只有在WinEditScreen不活跃时才在首页显示成功弹窗
      if (!GlobalVars.isWinEditScreenActive) {
        // 确保关闭支付二维码弹窗
        PGDialog.dismiss(tag: DialogTags.purchaseQRCode);
        // 第一次购买成功已经弹过加微信
        final isFirstBuySuccess =
            _cachedProfileViewModel?.isFirstBuySuccess ?? false;
        PGLog.d(
            'ProjectHomeScreenPC - _handleOrderStatusChangeEvent isFirstBuySuccess $isFirstBuySuccess');
        if (!isFirstBuySuccess) {
          // 如果购买成功弹窗没有显示，则显示（第一次购买成功(暂时未定)有流程，不能强制关闭已有的）
          if (!PGDialog.isDialogVisible(DialogTags.purchaseSuccess)) {
            PurchaseSuccessDialog.show(null);
          }
        }
      }
    }
    // 处理订单取消、退款事件
    else if (event.status == OrderStatus.canceled ||
        event.status == OrderStatus.refunded) {
      // 显示提示
      PGDialog.showToast(event.message);
    }
  }

  _reportPayResult(OrderEvent event) {
    final PageAction action;
    if (event.status == OrderStatus.completed) {
      action = PageAction.pay_success;
    } else if (event.status == OrderStatus.canceled ||
        event.status == OrderStatus.refunded) {
      action = PageAction.pay_failed;
    } else {
      return;
    }
    final userId =
        context.read<CurrentUserRepository>().user?.effectiveId ?? "";
    var failedReason = "";
    if (action == PageAction.pay_failed) {
      failedReason = event.message;
    }

    recordPayActionLog(
        userId: userId,
        payActionId: "",
        pageStyle: "no_use",
        pageAction: action,
        itemId: event.purchasePlanId,
        itemName: event.purchasePlanName,
        itemPrice: event.purchasePlanPrice,
        orderId: event.orderId,
        failedReason: failedReason);
  }

  // 检查是否新用户权益弹窗
  void _checkAndGetNewUserBenefits(ProfileDialogViewModel profileViewModel) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final needShowNewUserGiftDialog =
          profileViewModel.getNeedShowNewUserGiftDialog();
      if (mounted && needShowNewUserGiftDialog) {
        profileViewModel.getNewUserBenefits().then((activity) {
          PGLog.d(
              'ProjectHomeScreenPC - _checkAndGetNewUserBenefits activity: $activity');
          if (activity != null &&
              activity.count != null &&
              activity.count! > 0) {
            // 真正弹成功才算并记录进当前用户缓存
            UserPreferencesService.setHasShowNewUserGift(hasShown: true);
            PGLog.d('新人弹窗 show getNewUserBenefits ${activity.count}');

            // 显示新人福利弹窗，并在关闭后检查新手引导
            NewUserBenefitDialog.show(context, activity.count!, onClose: () {
              if (mounted) {
                _handleFirstStepDemoProjectNoviceGuide(context);
              }
            });
          } else {
            PGLog.d('新人弹窗 没有数据 show getNewUserBenefits 没有信息');
          }
        }).catchError((error) {
          PGLog.e('获取新用户权益失败: $error');
        });
      }
    });
  }

  // 新手引导模型初始化并开始处理是否展示等逻辑
  void _initNoviceModelAndStartHandNovice() {
    _cachedNoviceGuideFirstStepViewModel = NoviceGuideDemoProjectViewModel(
      context.read<NoviceGuideManager>(),
      context.read<ProjectUseCaseProvider>(),
      context.read<CurrentUserRepository>(),
      context.read<UnityController>(),
      context.read<ProjectStateProvider>(),
      context.read<UnityUseCaseProvider>(),
      context.read<WorkspaceUseCaseProvider>(),
      context.read<MediaRepository>(),
      context.read<ProjectRepository>(),
    );

    if (_cachedNoviceGuideFirstStepViewModel != null && mounted) {
      _handleFirstStepDemoProjectNoviceGuide(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    PGLog.d('ProjectHomeScreenPC - 刷新');
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) {
            _cachedProfileViewModel = ProfileDialogViewModel(
              context.read<CurrentUserRepository>(),
              context.read<AccountRepository>(),
              context.read<WechatGiftRepository>(),
              context.read<OpsCustomTableRepository>(),
              context.read<AccountRightsStateProvider>(),
              context.read<RewardRepository>(),
              context.read<NewUserRepository>(),
              context.read<AuthUseCaseProvider>(),
            );
            // 设置账号变更监听器
            _cachedProfileViewModel!.setAccountChangeListener(this);
            return _cachedProfileViewModel!;
          },
        ),
        ChangeNotifierProvider(
          create: (context) {
            _cachedHomeViewModel = HomeViewModel(
              GoRouterNavigatorService(context),
              context.read<AuthRepository>(),
              context.read<ProjectRepository>(),
              context.read<ProjectUseCaseProvider>(),
              context.read<UnityUseCaseProvider>(),
              context.read<CurrentUserRepository>(),
              context.read<ProjectStateProvider>(),
              context.read<UnityController>(),
              context.read<PhotoThumbnailRepository>(),
              context.read<VersionIntroRepository>(),
              context.read<WorkspaceUseCaseProvider>(),
              context.read<NoviceGuideManager>(),
              context.read<MediaRepository>(),
            );
            return _cachedHomeViewModel!;
          },
        ),
      ],
      child: Row(
        children: [
          Expanded(
            child: Consumer<HomeViewModel>(
              builder: (context, viewModel, child) {
                PGLog.d('ProjectHomeScreenPC - 刷新3');
                // 计算网格布局（只在屏幕宽度变化时重新计算）
                final screenWidth = MediaQuery.of(context).size.width;
                if (_cachedScreenWidth != screenWidth) {
                  _cachedScreenWidth = screenWidth;
                  viewModel.calculatePcGrid(screenWidth);
                }

                // 获取布局参数
                final sideMargin = viewModel.sideMargin;

                // 直接响应 isWaitingForUnity 状态变化
                if (viewModel.isWaitingForUnity) {
                  // 使用不依赖于帧刷新的方式显示 loading
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      showLoadingForUnityLaunch = true;
                      if (_isLoading) {
                        PGDialog.dismiss(tag: DialogTags.loading);
                      }
                      PGDialog.showLoading();
                      _isLoading = true;
                    }
                  });
                } else {
                  if (showLoadingForUnityLaunch) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        PGDialog.dismiss(tag: DialogTags.loading);
                        _isLoading = false;
                      }
                    });
                    showLoadingForUnityLaunch = false;
                  }
                }

                return TitleBarWidget(
                  backgroundColor: const Color(0xFF0D0D0D),
                  funcWidget: ProjectHomeTitleBarOption(
                    key: _titleBarOptionKey,
                    profileClick: () =>
                        ProfileViewClickService.handleUserAccountClick(
                      context: context,
                      profileViewKey: _profileViewKey,
                      userCardKey: _userCardKey,
                    ),
                  ),
                  child: Container(
                    color: const Color(0xFF0D0D0D),
                    child: MouseEventHandlerWidget(
                      canProcessFiles: () => _cachedHomeViewModel != null
                          ? _canProcessFiles(_cachedHomeViewModel!)
                          : false,
                      showDragForbiddenOverlay: _cachedHomeViewModel != null
                          ? !_cachedHomeViewModel!.isBatchProcessing
                          : true,
                      child: RouteAwareDragHandlerWidget(
                        canProcessFiles: () => _cachedHomeViewModel != null
                            ? _canProcessFiles(_cachedHomeViewModel!)
                            : false,
                        showDragForbiddenOverlay: _cachedHomeViewModel != null
                            ? !_cachedHomeViewModel!.isBatchProcessing
                            : true,
                        onDragDone: (result) async {
                          // 检查是否可以处理文件（没有对话框打开且未处于批处理模式）
                          if (_cachedHomeViewModel != null &&
                              _canProcessFiles(_cachedHomeViewModel!) &&
                              mounted) {
                            if (result.validFiles.isNotEmpty) {
                              PGDialog.showLoading();
                              if (result.isTapjImport) {
                                // 处理tapj文件导入
                                final tapjData = result.tapjData!;
                                await _cachedHomeViewModel!.handleImportProject(
                                  context,
                                  tapjData.importData,
                                  tapjData.historyFiles,
                                );
                              } else {
                                final processResult =
                                    await _cachedHomeViewModel!
                                        .processSelectedFiles2(
                                            result.validFiles,
                                            result.projectName,
                                            null);
                                if (processResult is ErrorStatus &&
                                    processResult.errorType ==
                                        ProcessFilesErrorType.diskSpace) {
                                  PGDialog.showToast(processResult.message);
                                }
                              }
                              await PGDialog.dismiss();
                            }
                          }
                        },
                        child: Row(
                          children: [
                            Consumer<ProfileDialogViewModel>(
                              builder: (context, profileViewModel, child) {
                                // 构建左侧个人中心
                                return _buildProfileView();
                              },
                            ),
                            Expanded(
                              child: Consumer<HomeViewModel>(
                                builder: (context, viewModel, child) {
                                  PGLog.d('ProjectHomeScreenPC - 刷新2');
                                  // 修复显示了小的创建按钮后，拖动窗口，重绘时，
                                  // _showCreateProjectOnly状态不正确
                                  // 导致小的创建按钮显示的问题
                                  // 判断滚动位置并设置_showCreateProjectOnly
                                  if (_scrollController.hasClients &&
                                      _isNormalMode) {
                                    final offset = _scrollController.offset;
                                    _showCreateProjectOnly =
                                        offset >= headerHeight;
                                  }
                                  // 处理待处理的外部消息 - 只在有消息时处理
                                  if (viewModel.hasPendingExternalMessages) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) async {
                                      if (mounted) {
                                        // 不再在这里显示loading，而是在用户选择操作后再显示
                                        await viewModel
                                            .processPendingExternalMessages(
                                                context);
                                      }
                                    });
                                  }

                                  return Container(
                                    color: const Color(0xFF0D0D0D),
                                    child: Stack(
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(
                                              top: _showCreateProjectOnly
                                                  ? 6
                                                  : (_isHeaderVisible
                                                      ? 30
                                                      : 18)),
                                          child: CustomScrollView(
                                            controller: _scrollController,
                                            physics:
                                                const AlwaysScrollableScrollPhysics(),
                                            slivers: [
                                              if (_showHeader) ...[
                                                SliverToBoxAdapter(
                                                  child: AnimatedOpacity(
                                                    opacity: _isHeaderVisible
                                                        ? 1.0
                                                        : 0.0,
                                                    duration: const Duration(
                                                        milliseconds: 300),
                                                    child: ProjectHomePcHeader(
                                                      sideMargin: sideMargin,
                                                      onNewProject: () => {
                                                        _handleImageSelection(
                                                            context),
                                                      },
                                                      onBatchEdit: () {
                                                        _handleBatchProcess(
                                                          viewModel,
                                                        );
                                                      },
                                                      onAiEdit: () {
                                                        // 跳转到AI场景增强页面
                                                        context.push(
                                                            Routes.aigcProject);
                                                      },
                                                      onExportProgress: () {
                                                        // ExportDialog.show();
                                                        // 处理导出进度按钮点击
                                                        ExportListDialog2
                                                            .show();
                                                      },
                                                    ),
                                                  ),
                                                ),
                                                const SliverToBoxAdapter(
                                                  child: SizedBox(height: 12),
                                                ),
                                              ],
                                              SliverPersistentHeader(
                                                pinned: true,
                                                delegate: TopBarDelegate(
                                                  sideMargin: sideMargin,
                                                  topBarKey: _topBarKey,
                                                  isBatchProcessing: viewModel
                                                      .isBatchProcessing,
                                                  showCreateProjectOnly:
                                                      _showCreateProjectOnly,
                                                  onRecentClick: () =>
                                                      _handleSortButtonClick(
                                                          context),
                                                  onMultiSelectClick: () {
                                                    _handleBatchProcess(
                                                      viewModel,
                                                    );
                                                    // PGDialog.showToast('敬请期待');
                                                  },
                                                  onBatchPresetClick: () {
                                                    PresetDialog.show(context,
                                                        (preset, path) {
                                                      if (preset != null) {
                                                        viewModel
                                                            .batchExportWithPreset(
                                                          preset,
                                                          path ?? "",
                                                        );
                                                      }

                                                      _handleBatchProcessEnd(
                                                        viewModel,
                                                      );
                                                      PGDialog.showToast(
                                                          '正在导出，可以在进度中查看');
                                                    });
                                                  },
                                                  onAllClick: () {
                                                    viewModel.batchAllPreset();
                                                  },
                                                  onCancelClick: () {
                                                    _handleBatchProcessEnd(
                                                        viewModel);
                                                  },
                                                  onCreateProjectClick: () {
                                                    _handleImageSelection(
                                                        context);
                                                  },
                                                ),
                                              ),
                                              SliverPadding(
                                                padding: EdgeInsets.only(
                                                  left: sideMargin,
                                                  right: sideMargin,
                                                  bottom: 6,
                                                ),
                                                sliver: !viewModel.hasProjects
                                                    ? SliverFillRemaining(
                                                        hasScrollBody: false,
                                                        child: Stack(
                                                          children: [
                                                            // 背景图层
                                                            Positioned.fill(
                                                              child:
                                                                  Image.asset(
                                                                'assets/icons/empty_state_back.png',
                                                                fit: BoxFit
                                                                    .cover,
                                                              ),
                                                            ),
                                                            // 空状态组件
                                                            Center(
                                                              child:
                                                                  ProjectHomeEmptyState(
                                                                onSelectFiles: () =>
                                                                    _handleImageSelection(
                                                                        context),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      )
                                                    : ProjectHomePCGridView(
                                                        onBatchEdit: ({
                                                          required int index,
                                                        }) {
                                                          _handleBatchProcess(
                                                            viewModel,
                                                            index: index,
                                                          );
                                                        },
                                                      ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const PreloadUnityWidget(),
        ],
      ),
    );
  }

  Widget _buildProfileView() {
    return ProjectHomePcProfileView(
      key: _profileViewKey,
      userCardKey: _userCardKey,
      // 点击用户账号展示个人中心功能列表弹窗
      onUserAccountClick: () => ProfileViewClickService.handleUserAccountClick(
        context: context,
        profileViewKey: _profileViewKey,
        userCardKey: _userCardKey,
      ),
      // 点击完善账号信息展示微信福利弹窗
      onCompleteAccountInfoClick: () =>
          ProfileViewClickService.handleShowWechatGiftDialog(
        context: context,
      ),
      // 点击购买按钮展示购买弹窗
      onPurchaseClick: (defaultTab) =>
          ProfileViewClickService.handleBuyPackageClick(
        context,
        sourceType: SourceType.home_page,
        defaultTab: defaultTab,
      ),
      // 点击全部项目
      onAllProjectsClick: () => {},
      // 点击快捷键指南展示快捷键指南弹窗
      onGuideClick: () => ProfileViewClickService.handleGuideClick(),
      // 点击快捷键展示快捷键弹窗
      onShortKeyClick: () => ProfileViewClickService.handleShortKeyClick(),
      // 点击客服展示客服弹窗
      onCustomerServiceClick: () =>
          ProfileViewClickService.handleCustomerServiceClick(),
      // 点击检查更新展示检查更新弹窗
      onCheckUpdateClick: () => ProfileViewClickService.handleCheckUpdateClick(
        context: context,
      ),
      // 点击版本介绍展示版本介绍弹窗
      onVersionIntroduceClick: () =>
          ProfileViewClickService.handleVersionIntroduceClick(
        context: context,
      ),
    );
  }

  void _showAddWechatToastIfNeeded(ProfileDialogViewModel profileViewModel) {
    if (profileViewModel.isWechatAddFirstTime()) {
      PGLog.d(
          'ProjectHomeScreenPC - _checkAddWechatAndNewUserBonusStatus 检测到已经添加微信，显示 toast并关闭弹窗');
      // 检测到已经添加微信，显示 toast并关闭弹窗
      PGDialog.showToast('成功添加微信');
      PGDialog.dismiss(tag: DialogTags.wechatGift);
    }
  }

  /// 处理排序按钮点击
  void _handleSortButtonClick(BuildContext context) {
    final viewModel = context.read<HomeViewModel>();

    // 获取 ProjectHomePCTopBar 实例
    final topBar = _topBarKey.currentWidget as ProjectHomePCTopBar?;
    if (topBar == null) {
      PGLog.d('topBar == null');
      return;
    }

    // 获取 recentButton 的 RenderBox
    final RenderBox? renderBox =
        ProjectHomePCTopBar.recentButtonKey.currentContext?.findRenderObject()
            as RenderBox?;
    if (renderBox == null) {
      PGLog.d('renderBox == null');
      return;
    }

    // 获取排序按钮在屏幕上的位置
    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonSize = renderBox.size;

    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => Stack(
        children: [
          Positioned(
            left: buttonPosition.dx,
            top: buttonPosition.dy + buttonSize.height + 8,
            child: ProjectSortDialog(
              selectedOption: viewModel.currentSortOption,
              onOptionSelected: (option) {
                viewModel.setSortOption(option);
                viewModel.loadProjects();
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    ).then((_) {
      // 添加Dialog关闭监听，刷新topBar排序按钮状态(无论通过哪种方式关闭Dialog，都会触发这个回调,包括Navigator.pop(context))
      _topBarKey.currentState?.resetSelectedState();
    });
  }

  void _showWechatGiftDialogIfNeeded(ProfileDialogViewModel profileViewModel) {
    final isFirstNeedPopWechatGiftDialog =
        profileViewModel.checkIsFirstNeedPopWechatGiftDialogStatus();
    if (isFirstNeedPopWechatGiftDialog) {
      if (profileViewModel.isFirstExportSuccess) {
        ProfileViewClickService.handleShowWechatGiftDialog(
          context: context,
        );
        // 记录已经显示过微信礼包弹窗
        UserPreferencesService.setHasShownWechatGiftDialog(hasShown: true);
        return;
      }
      if (profileViewModel.isFirstBuySuccess) {
        PurchaseSuccessDialog.show(() async {
          // 购买成功弹窗关闭后，再显示微信礼包弹窗
          ProfileViewClickService.handleShowWechatGiftDialog(
            context: context,
          );
          // 记录已经显示过微信礼包弹窗
          UserPreferencesService.setHasShownWechatGiftDialog(hasShown: true);
        });
      }
    }
  }

  /// 检查是否可以处理文件操作
  /// 当有对话框打开(只有需要手动关闭的弹窗出现需要禁止拖)或处于批处理模式时，不应处理文件操作
  /// Toast不应阻止文件处理操作
  bool _canProcessFiles(HomeViewModel viewModel) {
    // 如果处于批处理模式，不能处理文件
    if (viewModel.isBatchProcessing) {
      return false;
    }

    // 如果没有任何对话框，可以处理文件
    if (!PGDialog.hasOpenDialog) {
      return true;
    }

    // 检查是否只有Toast对话框打开
    return _isOnlyToastOpen();
  }

  /// 检查当前是否只有Toast对话框打开
  bool _isOnlyToastOpen() {
    // 检查是否有Toast显示
    final hasDesktopToast = PGDialog.isDialogVisible(DialogTags.desktopToast);
    final hasDesktopToastDismiss =
        PGDialog.isDialogVisible(DialogTags.desktopToastDismiss);

    return hasDesktopToast || hasDesktopToastDismiss;
  }

  /// 处理图片选择
  Future<void> _handleImageSelection(BuildContext context) async {
    PGLog.d('ProjectHomeScreenPC - _handleImageSelection');
    final viewModel = context.read<HomeViewModel>();
    if (!mounted) {
      PGLog.d('mounted == false');
      return;
    }
    final buildContext = context;
    // PC直接从文件获取
    final dealImageFilesResult = await viewModel.pickImagesFromFiles();
    PGLog.d('ProjectHomeScreenPC - _handleImageSelection files selected');
    if (dealImageFilesResult != null &&
        dealImageFilesResult.validFiles.isNotEmpty &&
        mounted) {
      PGDialog.showLoading();

      if (dealImageFilesResult.isTapjImport && buildContext.mounted) {
        // 处理tapj文件导入
        final tapjData = dealImageFilesResult.tapjData!;
        await viewModel.handleImportProject(
          buildContext,
          tapjData.importData,
          tapjData.historyFiles,
        );
      } else {
        // 处理普通图片文件
        final result = await viewModel.processSelectedFiles2(
            dealImageFilesResult.validFiles,
            dealImageFilesResult.projectName,
            null);
        if (result is ErrorStatus &&
            result.errorType == ProcessFilesErrorType.diskSpace) {
          PGDialog.showToast(result.message);
        }
      }

      await PGDialog.dismiss();
    }
  }

  /// 检查滚动位置并更新状态，用于窗口大小变化时调用
  void _checkScrollPositionAndUpdateState() {
    if (!mounted || !_isNormalMode) {
      return;
    }

    // 如果滚动控制器已附加，检查当前滚动位置
    if (_scrollController.hasClients) {
      final offset = _scrollController.offset;
      final shouldShowCreateProjectOnly = offset >= headerHeight;

      // 只有当状态需要变化时才更新
      if (shouldShowCreateProjectOnly != _showCreateProjectOnly) {
        setState(() {
          _showCreateProjectOnly = shouldShowCreateProjectOnly;
        });
      }
    } else {
      // 如果滚动控制器未附加（例如初始化时），确保状态为默认值
      if (_showCreateProjectOnly) {
        setState(() {
          _showCreateProjectOnly = false;
        });
      }
    }
  }

  // 检查并处理新手引导逻辑
  Future<void> _handleFirstStepDemoProjectNoviceGuide(
      BuildContext context) async {
    if (_cachedNoviceGuideFirstStepViewModel == null) {
      PGLog.d('NoviceGuideDemoProjectViewModel == null');
      return;
    }
    // handleFirstStepDemoProjectNoviceGuide这里面有刷新ui的notifier，获取位置需要等下一帧，才能有数据
    _cachedNoviceGuideFirstStepViewModel!
        .handleFirstStepDemoProjectNoviceGuide()
        .then((result) {
      if (result.shouldShow &&
          result.projectInfo != null &&
          result.guideInfo != null &&
          result.projectIndex != null &&
          context.mounted) {
        // 新手引导完成，更新新手引导状态
        showNoviceGuideMask(
            context: context,
            project: result.projectInfo!,
            projectIndex: result.projectIndex!,
            guideInfo: result.guideInfo!);
      }
    });
  }

  /// 显示引导蒙层
  void showNoviceGuideMask({
    required BuildContext context,
    required ProjectInfo project,
    required int projectIndex,
    required NoviceGuideInfo guideInfo,
  }) {
    // 依赖父方法里面loadProject，获取位置需要等下一帧，才能有数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 使用索引获取项目位置
      final Rect? targetRect = ProjectHomePCGridView.getItemRect(projectIndex);

      if (targetRect == null) {
        PGLog.d('showGuideMask - 无法获取项目位置');
        return;
      }

      // 创建进入编辑页面的回调
      onEnterEdit() {
        if (context.mounted && _cachedHomeViewModel != null) {
          // 获取项目在列表中的索引
          _cachedHomeViewModel!.selectedProjectInfo(project);
        }
      }

      NoviceGuideUIService.showGuide(
        context: context,
        step: guideInfo.step,
        totalSteps: guideInfo.totalSteps,
        project: project,
        targetRect: targetRect,
        description: guideInfo.description,
        coverThumbnailPath: NoviceGuideManager.demoProjectThumbnailPath,
        markStepShown: _cachedHomeViewModel!.noviceGuideManager.markStepShown,
        markGuideCompleted:
            _cachedHomeViewModel!.noviceGuideManager.markGuideCompleted,
        onDismiss: () {
          PGLog.d('新手引导步骤${guideInfo.step}已关闭');
        },
        onEnterEdit: onEnterEdit, // 添加进入编辑页面的回调
      );
    });
  }
}

class TopBarDelegate extends SliverPersistentHeaderDelegate {
  final double sideMargin;
  final GlobalKey<ProjectHomePCTopBarState> topBarKey;
  final bool isBatchProcessing;
  final bool showCreateProjectOnly;
  final VoidCallback onRecentClick;
  final VoidCallback onMultiSelectClick;
  final VoidCallback onBatchPresetClick;
  final VoidCallback onAllClick;
  final VoidCallback onCancelClick;
  final VoidCallback onCreateProjectClick;

  TopBarDelegate({
    required this.sideMargin,
    required this.topBarKey,
    required this.isBatchProcessing,
    required this.showCreateProjectOnly,
    required this.onRecentClick,
    required this.onMultiSelectClick,
    required this.onBatchPresetClick,
    required this.onAllClick,
    required this.onCancelClick,
    required this.onCreateProjectClick,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: showCreateProjectOnly ? 86.0 : 56.0,
      color: const Color(0xFF0D0D0D),
      child: Column(
        children: [
          isBatchProcessing
              ? ProjectHomeMutiChoiceTopBar(
                  sideMargin: sideMargin,
                  batchPresetClick: onBatchPresetClick,
                  allClick: onAllClick,
                  cancelClick: onCancelClick,
                )
              : ProjectHomePCTopBar(
                  topBarKey: topBarKey,
                  sideMargin: sideMargin,
                  showCreateProjectOnly: showCreateProjectOnly,
                  onCreateProjectClick: onCreateProjectClick,
                  onRecentClick: onRecentClick,
                  onMultiSelectClick: onMultiSelectClick,
                ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => showCreateProjectOnly ? 86.0 : 56.0;

  @override
  double get minExtent => showCreateProjectOnly ? 86.0 : 56.0;

  @override
  bool shouldRebuild(covariant TopBarDelegate oldDelegate) {
    return oldDelegate.isBatchProcessing != isBatchProcessing ||
        oldDelegate.sideMargin != sideMargin ||
        oldDelegate.showCreateProjectOnly != showCreateProjectOnly;
  }
}
